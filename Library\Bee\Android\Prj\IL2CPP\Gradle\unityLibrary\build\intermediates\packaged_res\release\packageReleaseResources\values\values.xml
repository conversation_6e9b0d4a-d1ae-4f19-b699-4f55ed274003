<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="staticSplashScreenBackgroundColor">#231F20</color>
    <item name="unitySurfaceView" type="id"/>
    <string name="FreeformWindowOrientation_landscape"/>
    <string name="FreeformWindowOrientation_portrait"/>
    <string name="FreeformWindowSize_maximize"/>
    <string name="FreeformWindowSize_phone"/>
    <string name="FreeformWindowSize_tablet"/>
    <string name="game_view_content_description">Game view</string>
    <style name="BaseUnityGameActivityTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowBackground">@android:color/black</item>
  </style>
    <style name="BaseUnityGameActivityTheme.Translucent" parent="@style/BaseUnityGameActivityTheme">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style>
    <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen"/>
    <style name="UnityThemeSelector" parent="BaseUnityTheme">
    <item name="android:windowBackground">@android:color/black</item>
  </style>
    <style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style>
</resources>