{"totalVariantsIn": 582, "totalVariantsOut": 582, "shaders": [{"inputVariants": 9, "outputVariants": 9, "name": "VFXCopyBuffer", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.00030000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "ProbeVolumeSamplingDebugPositionNormal", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 21, "outputVariants": 21, "name": "GenSdfRayMap", "pipelines": [{"inputVariants": 21, "outputVariants": 21, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "VFXPrefixSum", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "VFXFillIndirectArgs", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "UpdateStrips", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Sort", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.0005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[RadialWrap] [RadialWrap] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ReferenceImpl.IndexingOpsA", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Tile", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherElementsFast", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElementsFast", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElements", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Expand", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Slice", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "TensorToTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureExact", "stripTimeMs": 0.0007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureLinear", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Resize", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Floor", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Ceil", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Linear_None", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Floor", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Ceil", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Linear_None", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Floor", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Ceil", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Linear_None", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Floor", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Ceil", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Linear_None", "stripTimeMs": 0.0}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombRed] [Muzzle] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "GemmT", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_T8x8_R4x4", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_WT_T8x8_R4x4", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_WT_T8x8_R4x4", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Up] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "ImageBased", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceDepthColumnRow", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceColumnRowDepth", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SpaceToDepth", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Bubbles] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "ReferenceImpl", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ReduceIndices", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatFirst", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatFirst", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatLast", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatLast", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntFirst", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntFirst", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntLast", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntLast", "stripTimeMs": 0.0}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "IndexingOps", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OneHot", "stripTimeMs": 0.0005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherND", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SliceSet", "stripTimeMs": 0.0}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "AxisActivations", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LogSoftmaxEnd", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SoftmaxEnd", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardmaxEnd", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Normalization", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LayerNormalizationTail", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RMSNormalizationTail", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BatchNormalization", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScaleBias", "stripTimeMs": 0.0}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Random", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomUniform", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomNormal", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BernoulliFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>lliInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TopP", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "NMS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCorners", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCenter", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSSelect", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSCompact", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "ReferenceImpl.PadA", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadBorderND", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadReflectND", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadSymmetricND", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadEdgeND", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadWrapND", "stripTimeMs": 0.0}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "RNN", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LSTMEnd", "stripTimeMs": 0.0005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "CumSum", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardInclusive", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseInclusive", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardExclusive", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseExclusive", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardInclusive", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseInclusive", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardExclusive", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseExclusive", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "ScatterOps", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 10, "outputVariants": 10, "variantName": "Kernel: ScatterND", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 19, "outputVariants": 19, "name": "Compute.Shaders.ReductionUnrolled.gen", "pipelines": [{"inputVariants": 19, "outputVariants": 19, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxFloat", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanSquareFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Float", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL2Float", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSqrtFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumExpFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumExpFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Int", "stripTimeMs": 0.0}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Strip] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombBlue] [Muzzle] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "<PERSON><PERSON>", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T16x16_R4x4", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T16x16_R4x4", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T8x8_R4x4", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T8x8_R4x4", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T16x16_R4x4", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T8x8_R4x4", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T16x16_R4x4", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T8x8_R4x4", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_V_L1Cached64", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_V_L1Cached64", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_V_L1Cached64", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_V_L1Cached64", "stripTimeMs": 0.0}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ReferenceImpl.GenericA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InstanceNormalizationTail", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "GroupConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: GroupedConv3D", "stripTimeMs": 0.0007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: GroupedConv2D", "stripTimeMs": 0.0001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: GroupedConv1D", "stripTimeMs": 0.0}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: GroupedConv3D_AlignsTo64", "stripTimeMs": 0.0}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: GroupedConv2D_AlignsTo64", "stripTimeMs": 0.0001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: GroupedConv1D_AlignsTo64", "stripTimeMs": 0.0}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BitonicSort", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortStep", "stripTimeMs": 0.0008}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortKeyStep", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "ConvGeneric", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: Conv3D_Generic", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: Conv2D_Generic", "stripTimeMs": 0.0}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: Conv1D_Generic", "stripTimeMs": 0.0}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: Conv3D_1x1_Generic", "stripTimeMs": 0.0001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: Conv2D_1x1_Generic", "stripTimeMs": 0.0001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: Conv1D_1x1_Generic", "stripTimeMs": 0.0}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ConvTranspose3D_Generic", "stripTimeMs": 0.0}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ConvTranspose2D_Generic", "stripTimeMs": 0.0001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ConvTranspose1D_Generic", "stripTimeMs": 0.0}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [Coin] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 60, "outputVariants": 60, "name": "Compute.Shaders.Broadcast.gen", "pipelines": [{"inputVariants": 60, "outputVariants": 60, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPRelu", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPRelu", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePRelu", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMeanFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMeanFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMeanFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModInt", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "SortingOps", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: TopK", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "CopyOps", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Split", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopy", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopyStride", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemSet", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose2D", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastHalfToFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DequantizeUint8", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "TextureToTensor", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorExact", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorLinear", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "DepthwiseConv", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DWinograd", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KernelWinoExpand", "stripTimeMs": 0.0001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DDirect", "stripTimeMs": 0.0}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Compute.Shaders.ConvTranspose.gen", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose2D_KxK", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose1D_KxK", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Pool", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePoolReduce", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAveragePool", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPoolReduce", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalMaxPool", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AverageVariancePoolReduce", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAverageVariancePool", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxReduce", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalArgMaxReduce", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Bubbles] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "ReferenceImpl.Logical", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Where", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 55, "outputVariants": 55, "name": "Compute.Shaders.PointwiseUnary.gen", "pipelines": [{"inputVariants": 55, "outputVariants": 55, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LeakyRelu", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>sh", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Relu6", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeluFast", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSigmoid", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>nk", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ThresholdedRelu", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softplus", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Floor", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Round", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Reciprocal", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Exp", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Log", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sqrt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acos", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acosh", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Cos", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sin", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softsign", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSwish", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsNaN", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastIntToFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastFloatToInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Not", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeInt", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ReferenceImpl.PoolA", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool2D", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool2D", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool1D", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool1D", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ReferenceImpl.Einsum", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumOne", "stripTimeMs": 0.001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumTwo", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "LogicalOps", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OrInt", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AndInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: XorInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsInf", "stripTimeMs": 0.0}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Compute.Shaders.Conv.gen", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_KxK", "stripTimeMs": 0.0007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_1x1", "stripTimeMs": 0.0001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_KxK", "stripTimeMs": 0.0001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_1x1", "stripTimeMs": 0.0}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "RoiAlign", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: RoiAlign", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "GridSample", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample2D", "stripTimeMs": 0.0008}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample3D", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Up] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 38, "outputVariants": 38, "name": "Compute.Shaders.Reduction.gen", "pipelines": [{"inputVariants": 38, "outputVariants": 38, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxFloat", "stripTimeMs": 0.0004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanSquareFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanSquareFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Float", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Float", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL2Float", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL2Float", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSqrtFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSqrtFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumExpFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumExpFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumExpFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumExpFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Int", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Int", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "CompareOps", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterFloat", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterInt", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualFloat", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualInt", "stripTimeMs": 0.0001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualFloat", "stripTimeMs": 0.0}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualInt", "stripTimeMs": 0.0}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[RadialWrap] [RadialWrap] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombRed] [Muzzle] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Up] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Bubbles] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Strip] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombBlue] [Muzzle] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [Coin] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Bubbles] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Up] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[RadialWrap] [RadialWrap] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombRed] [Glow] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Down] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Fishes] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Gem] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombBlue] [Glow] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [CoinGlow] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Fishes] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Down] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombRed] [Glow] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Down] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Fishes] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Gem] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombBlue] [Glow] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [CoinGlow] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Fishes] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Down] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombRed] [Circle] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [HEADUP] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0004}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Trail Bodies] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Bubble] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombBlue] [Circle] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [StaticGlow] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Trail Bodies] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombRed] [Circle] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [HEADUP] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Trail Bodies] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Bubble] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_BombBlue] [Circle] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [StaticGlow] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0011}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Trail Bodies] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System (1)] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [HEADDOWN] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Stars] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Cicle] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [Circle] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Gems] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0011}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System (1)] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [HEADDOWN] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Stars] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rainbow] [Cicle] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Coin] [Circle] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Loose] [Gems] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [BubbleUp] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Gems] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Rainbow] [Gem] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Coin] [Coin] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Loose] [Fishes] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System (5)] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [BubbleUp] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Win] [Gems] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Loose] [Trail Bodies] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System (5)] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [BubbleDown] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Win] [Fishes] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Loose] [Gems] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System (6)] Initialize Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [BubbleDown] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Win] [Trail Bodies] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [System (6)] Update Particle", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0011}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Trail Bodies] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "[VFX_Win] [Gems] OutputUpdate", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Trail Bodies] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Trail Bodies] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Trail Bodies] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Trail Bodies (1)] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Trail Bodies (1)] Initialize Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Trail Bodies (1)] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Trail Bodies (1)] Update Particle Strip", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Up] GlobalSortKeys", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Up] GlobalSortKeys", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_UpDown] [Down] GlobalSortKeys", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "[VFX_Rocket_Distortion_Sides] [Down] GlobalSortKeys", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0009000000000000001}]}]}]}