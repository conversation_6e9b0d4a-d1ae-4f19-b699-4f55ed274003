1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ironsource.unity" >
4
5    <uses-sdk android:minSdkVersion="23" />
6
7    <uses-permission android:name="android.permission.INTERNET" />
7-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\AndroidManifest.xml:3:3-65
7-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\AndroidManifest.xml:3:20-62
8    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
8-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\AndroidManifest.xml:4:3-77
8-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\AndroidManifest.xml:4:20-74
9
10    <application>
10-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\AndroidManifest.xml:5:3-11:17
11
12        <!-- AdMob -->
13        <!-- As Requiered By Admob please add your App ID -->
14        <!-- <meta-data -->
15        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
16        <!-- android:value="YOUR_ADMOB_APP_ID"/> -->
17    </application>
18
19</manifest>
