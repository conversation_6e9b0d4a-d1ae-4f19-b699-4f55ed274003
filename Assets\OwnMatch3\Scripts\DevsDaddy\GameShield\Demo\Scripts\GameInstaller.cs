using DevsDaddy.GameShield.Core.Constants;
using DevsDaddy.GameShield.Core.Modules.Captcha;
using DevsDaddy.GameShield.Core.Modules.Time;
using DevsDaddy.GameShield.Core.Payloads;
using DevsDaddy.GameShield.Demo.Enums;
using DevsDaddy.GameShield.Demo.Payloads;
using DevsDaddy.Shared.EventFramework;
using UnityEngine;

namespace DevsDaddy.GameShield.Demo
{
    /// <summary>
    /// Game Installer Demo Class
    /// </summary>
    internal class GameInstaller : MonoBehaviour
    {
        /// <summary>
        /// On Scene Installer Awake
        /// </summary>
        private void Awake()
        {
            BindEvents();
            Core.GameShield.Main.AddModule<TimeProtector>().PauseDetector(false);
        }

        /// <summary>
        /// On Scene Installer Destroy
        /// </summary>
        private void OnDestroy()
        {
            UnbindEvents();
        }

        /// <summary>
        /// Bind Events
        /// </summary>
        private void BindEvents()
        {
            EventMessenger.Main.Subscribe<SecurityWarningPayload>(OnCheatDetected);
        }

        /// <summary>
        /// Unbind Events
        /// </summary>
        private void UnbindEvents()
        {
            EventMessenger.Main.Unsubscribe<SecurityWarningPayload>(OnCheatDetected);
        }

        /// <summary>
        /// On Cheat Detected
        /// </summary>
        /// <param name="payload"></param>
        private void OnCheatDetected(SecurityWarningPayload payload)
        {
            EventMessenger.Main.Publish(new RequestDetectionView
            {
                DetectionData = payload
            });
            //Application.Quit();
        }
    }
}