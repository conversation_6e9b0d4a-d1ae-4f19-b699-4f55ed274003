{"logs": [{"outputFile": "com.PhantomTeam.PufflandAdventure.launcher-mergeReleaseResources-40:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2b4e023067e1255755d74798b3b5ddb\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2368,2384,2390,4418,4434", "startColumns": "4,4,4,4,4", "startOffsets": "147659,148084,148262,223233,223644", "endLines": "2383,2389,2399,4433,4437", "endColumns": "24,24,24,24,24", "endOffsets": "148079,148257,148541,223639,223766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b619e24824560d5d58a3c53651fefebd\\transformed\\jetified-mediation-sdk-8.10.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,104", "endLines": "2,7", "endColumns": "48,24", "endOffsets": "99,318"}, "to": {"startLines": "609,3949", "startColumns": "4,4", "startOffsets": "32327,206572", "endLines": "609,3953", "endColumns": "48,24", "endOffsets": "32371,206786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5fb357593bf2e41df09357350faade82\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "63,124,125,318,365,366,371,372,373,374,375,376,377,380,381,382,383,384,385,386,387,388,389,390,391,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,424,425,426,427,428,429,430,431,432,433,434,435,436,437,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,533,534,535,536,537,538,539,540,541,557,558,559,560,561,562,563,564,600,601,602,603,605,610,611,614,632,639,640,641,642,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,700,702,703,704,705,706,713,721,722,726,730,734,739,745,752,756,760,765,769,773,777,781,785,789,795,799,805,809,815,819,824,828,831,835,841,845,851,855,861,864,868,872,876,880,884,885,886,887,890,893,896,899,903,904,905,906,907,910,912,914,916,921,922,926,932,936,937,939,951,952,956,962,966,967,968,972,999,1003,1004,1008,1036,1208,1234,1405,1431,1462,1470,1476,1492,1514,1519,1524,1534,1543,1552,1556,1563,1582,1589,1590,1599,1602,1605,1609,1613,1617,1620,1621,1626,1631,1641,1646,1653,1659,1660,1663,1667,1672,1674,1676,1679,1682,1684,1688,1691,1698,1701,1704,1708,1710,1714,1716,1718,1720,1724,1732,1740,1752,1758,1767,1770,1781,1784,1785,1790,1791,1804,1873,1943,1944,1954,1963,1964,1966,1970,1973,1976,1979,1982,1985,1988,1991,1995,1998,2001,2004,2008,2011,2015,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2041,2043,2044,2045,2046,2047,2048,2049,2050,2052,2053,2055,2056,2058,2060,2061,2063,2064,2065,2066,2067,2068,2070,2071,2072,2073,2074,2086,2088,2090,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2106,2107,2108,2109,2110,2111,2112,2114,2118,2138,2139,2140,2141,2142,2143,2147,2148,2149,2157,2159,2161,2163,2165,2167,2168,2169,2170,2172,2174,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2190,2191,2192,2193,2195,2197,2198,2200,2201,2203,2205,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2220,2221,2222,2223,2225,2226,2227,2228,2229,2231,2233,2235,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2254,2329,2332,2335,2338,2352,2358,2400,2403,2432,2459,2468,2532,2895,2923,2961,3599,3954,3978,3984,4003,4024,4148,4320,4326,4342,4348,4402,4441,4507,4543,4677,4689,4715", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2764,5305,5350,14172,16017,16072,16394,16458,16528,16589,16664,16740,16817,17055,17140,17222,17298,17374,17451,17529,17635,17741,17820,17900,17957,18686,18760,18835,18900,18966,19026,19087,19159,19232,19299,19367,19426,19485,19544,19603,19662,19716,19770,19823,19877,19931,19985,20171,20245,20324,20397,20471,20542,20614,20686,20759,20816,20874,20947,21021,21095,21238,21310,21383,21453,21524,21584,21645,21714,21783,21853,21927,22003,22067,22144,22220,22297,22362,22431,22508,22583,22652,22720,22797,22863,22924,23021,23086,23155,23254,23325,23384,23442,23499,23558,23622,23693,23765,23837,23909,23981,24048,24116,24184,24243,24306,24370,24460,24551,24611,24677,24744,24810,24880,24944,24997,25064,25125,25192,25305,25363,25426,25491,25556,25631,25704,25776,25820,25867,25913,25962,26023,26084,26145,26207,26271,26335,26399,26464,26527,26587,26648,26714,26773,26833,26895,26966,27026,27582,27668,27755,27845,27932,28020,28102,28185,28275,29344,29396,29454,29499,29565,29629,29686,29743,31920,31977,32025,32074,32182,32376,32423,32579,33530,33878,33942,34004,34064,34641,34715,34785,34863,34917,34987,35072,35120,35166,35227,35290,35356,35420,35491,35554,35619,35683,35744,35805,35857,35930,36004,36073,36148,36222,36296,36437,39126,39250,39328,39418,39506,39602,39847,40429,40518,40765,41046,41298,41583,41976,42453,42675,42897,43173,43400,43630,43860,44090,44320,44547,44966,45192,45617,45847,46275,46494,46777,46985,47116,47343,47769,47994,48421,48642,49067,49187,49463,49764,50088,50379,50693,50830,50961,51066,51308,51475,51679,51887,52158,52270,52382,52487,52604,52818,52964,53104,53190,53538,53626,53872,54290,54539,54621,54719,55376,55476,55728,56152,56407,56501,56590,56827,58851,59093,59195,59448,61604,72285,73801,84496,86024,87781,88407,88827,90088,91353,91609,91845,92392,92886,93491,93689,94269,95637,96012,96130,96668,96825,97021,97294,97550,97720,97861,97925,98290,98657,99333,99597,99935,100288,100382,100568,100874,101136,101261,101388,101627,101838,101957,102150,102327,102782,102963,103085,103344,103457,103644,103746,103853,103982,104257,104765,105261,106138,106432,107002,107151,107883,108055,108139,108475,108567,109352,114583,119954,120016,120594,121178,121269,121382,121611,121771,121923,122094,122260,122429,122596,122759,123002,123172,123345,123516,123790,123989,124194,124524,124608,124704,124800,124898,124998,125100,125202,125304,125406,125508,125608,125704,125816,125945,126068,126199,126330,126428,126542,126636,126776,126910,127006,127118,127218,127334,127430,127542,127642,127782,127918,128082,128212,128370,128520,128661,128805,128940,129052,129202,129330,129458,129594,129726,129856,129986,130098,130996,131142,131286,131424,131490,131580,131656,131760,131850,131952,132060,132168,132268,132348,132440,132538,132648,132700,132778,132884,132976,133080,133190,133312,133475,134449,134529,134629,134719,134829,134919,135160,135254,135360,135824,135924,136036,136150,136266,136382,136476,136590,136702,136804,136924,137046,137128,137232,137352,137478,137576,137670,137758,137870,137986,138108,138220,138395,138511,138597,138689,138801,138925,138992,139118,139186,139314,139458,139586,139655,139750,139865,139978,140077,140186,140297,140408,140509,140614,140714,140844,140935,141058,141152,141264,141350,141454,141550,141638,141756,141860,141964,142090,142178,142286,142386,142476,142586,142670,142772,142856,142910,142974,143080,143166,143276,143360,143619,146235,146353,146468,146548,146909,147142,148546,148624,149968,151329,151717,154560,164613,165888,167559,193294,206791,207542,207804,208319,208698,212976,220380,220609,221056,221271,222771,223891,226917,228308,232956,233296,234607", "endLines": "63,124,125,318,365,366,371,372,373,374,375,376,377,380,381,382,383,384,385,386,387,388,389,390,391,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,424,425,426,427,428,429,430,431,432,433,434,435,436,437,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,533,534,535,536,537,538,539,540,541,557,558,559,560,561,562,563,564,600,601,602,603,605,610,611,614,632,639,640,641,642,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,700,702,703,704,705,706,720,721,725,729,733,738,744,751,755,759,764,768,772,776,780,784,788,794,798,804,808,814,818,823,827,830,834,840,844,850,854,860,863,867,871,875,879,883,884,885,886,889,892,895,898,902,903,904,905,906,909,911,913,915,920,921,925,931,935,936,938,950,951,955,961,965,966,967,971,998,1002,1003,1007,1035,1207,1233,1404,1430,1461,1469,1475,1491,1513,1518,1523,1533,1542,1551,1555,1562,1581,1588,1589,1598,1601,1604,1608,1612,1616,1619,1620,1625,1630,1640,1645,1652,1658,1659,1662,1666,1671,1673,1675,1678,1681,1683,1687,1690,1697,1700,1703,1707,1709,1713,1715,1717,1719,1723,1731,1739,1751,1757,1766,1769,1780,1783,1784,1789,1790,1795,1872,1942,1943,1953,1962,1963,1965,1969,1972,1975,1978,1981,1984,1987,1990,1994,1997,2000,2003,2007,2010,2014,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2040,2042,2043,2044,2045,2046,2047,2048,2049,2051,2052,2054,2055,2057,2059,2060,2062,2063,2064,2065,2066,2067,2069,2070,2071,2072,2073,2074,2087,2089,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2105,2106,2107,2108,2109,2110,2111,2113,2117,2121,2138,2139,2140,2141,2142,2146,2147,2148,2149,2158,2160,2162,2164,2166,2167,2168,2169,2171,2173,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2189,2190,2191,2192,2194,2196,2197,2199,2200,2202,2204,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2219,2220,2221,2222,2224,2225,2226,2227,2228,2230,2232,2234,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2328,2331,2334,2337,2351,2357,2367,2402,2431,2458,2467,2531,2894,2898,2950,2988,3616,3977,3983,3989,4023,4147,4167,4325,4329,4347,4382,4413,4506,4526,4597,4688,4714,4721", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2814,5345,5394,14208,16067,16129,16453,16523,16584,16659,16735,16812,16890,17135,17217,17293,17369,17446,17524,17630,17736,17815,17895,17952,18010,18755,18830,18895,18961,19021,19082,19154,19227,19294,19362,19421,19480,19539,19598,19657,19711,19765,19818,19872,19926,19980,20034,20240,20319,20392,20466,20537,20609,20681,20754,20811,20869,20942,21016,21090,21165,21305,21378,21448,21519,21579,21640,21709,21778,21848,21922,21998,22062,22139,22215,22292,22357,22426,22503,22578,22647,22715,22792,22858,22919,23016,23081,23150,23249,23320,23379,23437,23494,23553,23617,23688,23760,23832,23904,23976,24043,24111,24179,24238,24301,24365,24455,24546,24606,24672,24739,24805,24875,24939,24992,25059,25120,25187,25300,25358,25421,25486,25551,25626,25699,25771,25815,25862,25908,25957,26018,26079,26140,26202,26266,26330,26394,26459,26522,26582,26643,26709,26768,26828,26890,26961,27021,27089,27663,27750,27840,27927,28015,28097,28180,28270,28361,29391,29449,29494,29560,29624,29681,29738,29792,31972,32020,32069,32120,32211,32418,32467,32620,33557,33937,33999,34059,34116,34710,34780,34858,34912,34982,35067,35115,35161,35222,35285,35351,35415,35486,35549,35614,35678,35739,35800,35852,35925,35999,36068,36143,36217,36291,36432,36502,39174,39323,39413,39501,39597,39687,40424,40513,40760,41041,41293,41578,41971,42448,42670,42892,43168,43395,43625,43855,44085,44315,44542,44961,45187,45612,45842,46270,46489,46772,46980,47111,47338,47764,47989,48416,48637,49062,49182,49458,49759,50083,50374,50688,50825,50956,51061,51303,51470,51674,51882,52153,52265,52377,52482,52599,52813,52959,53099,53185,53533,53621,53867,54285,54534,54616,54714,55371,55471,55723,56147,56402,56496,56585,56822,58846,59088,59190,59443,61599,72280,73796,84491,86019,87776,88402,88822,90083,91348,91604,91840,92387,92881,93486,93684,94264,95632,96007,96125,96663,96820,97016,97289,97545,97715,97856,97920,98285,98652,99328,99592,99930,100283,100377,100563,100869,101131,101256,101383,101622,101833,101952,102145,102322,102777,102958,103080,103339,103452,103639,103741,103848,103977,104252,104760,105256,106133,106427,106997,107146,107878,108050,108134,108470,108562,108840,114578,119949,120011,120589,121173,121264,121377,121606,121766,121918,122089,122255,122424,122591,122754,122997,123167,123340,123511,123785,123984,124189,124519,124603,124699,124795,124893,124993,125095,125197,125299,125401,125503,125603,125699,125811,125940,126063,126194,126325,126423,126537,126631,126771,126905,127001,127113,127213,127329,127425,127537,127637,127777,127913,128077,128207,128365,128515,128656,128800,128935,129047,129197,129325,129453,129589,129721,129851,129981,130093,130233,131137,131281,131419,131485,131575,131651,131755,131845,131947,132055,132163,132263,132343,132435,132533,132643,132695,132773,132879,132971,133075,133185,133307,133470,133627,134524,134624,134714,134824,134914,135155,135249,135355,135447,135919,136031,136145,136261,136377,136471,136585,136697,136799,136919,137041,137123,137227,137347,137473,137571,137665,137753,137865,137981,138103,138215,138390,138506,138592,138684,138796,138920,138987,139113,139181,139309,139453,139581,139650,139745,139860,139973,140072,140181,140292,140403,140504,140609,140709,140839,140930,141053,141147,141259,141345,141449,141545,141633,141751,141855,141959,142085,142173,142281,142381,142471,142581,142665,142767,142851,142905,142969,143075,143161,143271,143355,143475,146230,146348,146463,146543,146904,147137,147654,148619,149963,151324,151712,154555,164608,164743,167253,168911,193861,207537,207799,207999,208693,212971,213577,220604,220755,221266,222349,223078,226912,227656,230434,233291,234602,234805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5ad4be011b6d6f61903e7e1c3b25ce3e\\transformed\\jetified-gpgs-plugin-support-2.0.0\\res\\values\\values.xml", "from": {"startLines": "3,6", "startColumns": "4,4", "startOffsets": "115,198", "endLines": "5,8", "endColumns": "12,12", "endOffsets": "193,265"}, "to": {"startLines": "707,710", "startColumns": "4,4", "startOffsets": "39692,39775", "endLines": "709,712", "endColumns": "12,12", "endOffsets": "39770,39842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\2e921cd16d63f4e0d0a788ce4333e813\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "636", "startColumns": "4", "startOffsets": "33710", "endColumns": "53", "endOffsets": "33759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\640b75ad0d55ed1c3508a4ec4216dab9\\transformed\\jetified-activity-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "612,635", "startColumns": "4,4", "startOffsets": "32472,33650", "endColumns": "41,59", "endOffsets": "32509,33705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\30fbf7abc4070fa1e2ce7bca99307007\\transformed\\jetified-play-services-cronet-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endColumns": "122", "endOffsets": "288"}, "to": {"startLines": "645", "startColumns": "4", "startOffsets": "34259", "endColumns": "126", "endOffsets": "34381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\93ad3fd77c199090ce77aa90bdfe079f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "643,688", "startColumns": "4,4", "startOffsets": "34121,37686", "endColumns": "67,166", "endOffsets": "34184,37848"}}, {"source": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13,17,18,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,169,226,282,331,377,424,492,662,907,999,1142", "endLines": "2,3,4,5,6,7,8,9,12,16,17,20,24", "endColumns": "67,45,56,55,48,45,46,67,10,10,91,10,10", "endOffsets": "118,164,221,277,326,372,419,487,657,902,994,1137,1366"}, "to": {"startLines": "438,631,646,647,648,649,650,698,1796,1799,1803,2150,2153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21170,33484,34386,34443,34499,34548,34594,38979,108845,109015,109260,135452,135595", "endLines": "438,631,646,647,648,649,650,698,1798,1802,1803,2152,2156", "endColumns": "67,45,56,55,48,45,46,67,10,10,91,10,10", "endOffsets": "21233,33525,34438,34494,34543,34589,34636,39042,109010,109255,109347,135590,135819"}}, {"source": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\launcher\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2", "startColumns": "2", "startOffsets": "55", "endColumns": "53", "endOffsets": "106"}, "to": {"startLines": "679", "startColumns": "4", "startOffsets": "36590", "endColumns": "55", "endOffsets": "36641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\62592ff0bfa9a791250bc84e74e6fc93\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "392,393,394,395,396,397,398,399,680,681,682,683,684,685,686,687,689,690,691,692,693,694,695,696,697,3990,4383", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18015,18105,18185,18275,18365,18445,18526,18606,36646,36751,36932,37057,37164,37344,37467,37583,37853,38041,38146,38327,38452,38627,38775,38838,38900,208004,222354", "endLines": "392,393,394,395,396,397,398,399,680,681,682,683,684,685,686,687,689,690,691,692,693,694,695,696,697,4002,4401", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "18100,18180,18270,18360,18440,18521,18601,18681,36746,36927,37052,37159,37339,37462,37578,37681,38036,38141,38322,38447,38622,38770,38833,38895,38974,208314,222766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\aa6aef0b366c6464950ea2f6ac03ac66\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,124,130,131,132,133,136,139,142,143,146,149,150,151,152,153,156,159,160,161,162,168,173,176,179,180,181,186,187,188,191,194,195,198,201,204,207,208,209,212,215,216,221,222,228,233,236,239,240,241,242,243,244,245,246,247,248,249,250,266,272,273,274,275,276,283,289,290,291,294,299,300,308,309,310,311,312,313,314,315,324,325,326,332,333,339,343,344,345,346,347,356,360,361,362,380,566,694,700,704,874,1026,1039,1055,1080,1103,1106,1109,1112,1141,1168,1185,1271,1279,1292,1308,1312,1342,1355,1359,1369,1379,1423,1436,1440,1443,1459,1500,1535,1542,1559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,339,395,581,642,933,985,1035,1088,1136,1187,1242,1302,1367,1426,1488,1540,1601,1663,1709,1842,1894,1944,1995,2402,2714,2759,2818,3015,3072,3267,3448,3502,3559,3751,3809,4005,4061,4255,4312,4363,4585,4637,4692,4882,5098,5148,5200,5256,5462,5523,5583,5653,5786,5917,6045,6113,6242,6368,6430,6493,6561,6628,6751,6876,6943,7008,7073,7362,7543,7664,7785,7851,7918,8128,8197,8263,8388,8514,8581,8707,8834,8959,9086,9142,9207,9333,9456,9521,9729,9796,10084,10264,10384,10504,10569,10631,10693,10757,10819,10878,10938,10999,11060,11119,11179,11870,12121,12172,12221,12269,12327,12619,12849,12896,12956,13062,13242,13296,13631,13685,13741,13787,13834,13885,13944,13996,14326,14385,14439,14677,14732,15022,15161,15207,15262,15307,15351,15699,15836,15877,15922,16859,25449,31222,31597,31764,39466,46265,46962,47713,48588,49458,49524,49603,49678,51026,52013,52976,56913,57318,57789,58580,58743,60104,60668,60821,61280,61698,63711,64248,64398,64518,65165,66854,68275,68628,69370", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,63,64,69,70,75,80,81,82,87,88,93,94,99,100,101,107,108,109,114,120,121,122,123,129,130,131,132,135,138,141,142,145,148,149,150,151,152,155,158,159,160,161,167,172,175,178,179,180,185,186,187,190,193,194,197,200,203,206,207,208,211,214,215,220,221,227,232,235,238,239,240,241,242,243,244,245,246,247,248,249,265,271,272,273,274,275,282,288,289,290,293,298,299,307,308,309,310,311,312,313,314,323,324,325,331,332,338,342,343,344,345,346,355,359,360,361,379,565,693,699,703,873,1025,1038,1054,1079,1102,1105,1108,1111,1140,1167,1184,1270,1278,1291,1307,1311,1341,1354,1358,1368,1378,1422,1435,1439,1442,1458,1499,1534,1541,1558,1561", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "334,390,576,637,928,980,1030,1083,1131,1182,1237,1297,1362,1421,1483,1535,1596,1658,1704,1837,1889,1939,1990,2397,2709,2754,2813,3010,3067,3262,3443,3497,3554,3746,3804,4000,4056,4250,4307,4358,4580,4632,4687,4877,5093,5143,5195,5251,5457,5518,5578,5648,5781,5912,6040,6108,6237,6363,6425,6488,6556,6623,6746,6871,6938,7003,7068,7357,7538,7659,7780,7846,7913,8123,8192,8258,8383,8509,8576,8702,8829,8954,9081,9137,9202,9328,9451,9516,9724,9791,10079,10259,10379,10499,10564,10626,10688,10752,10814,10873,10933,10994,11055,11114,11174,11865,12116,12167,12216,12264,12322,12614,12844,12891,12951,13057,13237,13291,13626,13680,13736,13782,13829,13880,13939,13991,14321,14380,14434,14672,14727,15017,15156,15202,15257,15302,15346,15694,15831,15872,15917,16854,25444,31217,31592,31759,39461,46260,46957,47708,48583,49453,49519,49598,49673,51021,52008,52971,56908,57313,57784,58575,58738,60099,60663,60816,61275,61693,63706,64243,64393,64513,65160,66849,68270,68623,69365,69466"}, "to": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,64,65,66,71,72,77,82,83,84,89,90,95,96,101,102,103,109,110,111,116,122,123,126,127,133,134,135,136,139,142,145,146,149,152,153,154,155,156,159,162,163,164,165,171,176,179,182,183,184,189,190,191,194,197,198,201,204,207,210,211,212,215,218,219,224,225,231,236,239,242,243,244,245,246,247,248,249,250,251,252,253,269,275,276,277,278,280,287,293,294,295,298,303,304,312,313,314,315,316,317,319,320,329,330,331,337,338,344,348,349,350,351,352,361,608,633,2905,2989,3154,3282,3288,3292,3441,3586,3719,3735,3760,3783,3786,3789,3792,3819,3846,3863,4168,4176,4189,4205,4209,4239,4252,4256,4266,4276,4330,4414,4438,4527,4598,4635,4670,4722,4739", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,389,445,631,692,983,1035,1085,1138,1186,1237,1292,1352,1417,1476,1538,1590,1651,1713,1759,1892,1944,1994,2045,2452,2819,2864,2923,3120,3177,3372,3553,3607,3664,3856,3914,4110,4166,4360,4417,4468,4690,4742,4797,4987,5203,5253,5399,5455,5661,5722,5782,5852,5985,6116,6244,6312,6441,6567,6629,6692,6760,6827,6950,7075,7142,7207,7272,7561,7742,7863,7984,8050,8117,8327,8396,8462,8587,8713,8780,8906,9033,9158,9285,9341,9406,9532,9655,9720,9928,9995,10283,10463,10583,10703,10768,10830,10892,10956,11018,11077,11137,11198,11259,11318,11378,12038,12289,12340,12389,12437,12555,12847,13077,13124,13184,13290,13470,13524,13859,13913,13969,14015,14062,14113,14213,14265,14595,14654,14708,14946,15001,15203,15342,15388,15443,15488,15532,15880,32286,33562,164951,168916,175034,180699,181074,181241,186471,192597,197446,198197,199051,199921,199987,200066,200141,200925,201816,202635,213582,213987,214458,215249,215412,216773,217337,217490,217949,218367,220760,223083,223771,227661,230439,231182,232603,234810,235552", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,126,132,133,134,135,138,141,144,145,148,151,152,153,154,155,158,161,162,163,164,170,175,178,181,182,183,188,189,190,193,196,197,200,203,206,209,210,211,214,217,218,223,224,230,235,238,241,242,243,244,245,246,247,248,249,250,251,252,268,274,275,276,277,278,286,292,293,294,297,302,303,311,312,313,314,315,316,317,319,328,329,330,336,337,343,347,348,349,350,351,360,364,608,633,2922,3153,3281,3287,3291,3440,3585,3598,3734,3759,3782,3785,3788,3791,3818,3845,3862,3948,4175,4188,4204,4208,4238,4251,4255,4265,4275,4319,4341,4417,4440,4542,4634,4669,4676,4738,4741", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "384,440,626,687,978,1030,1080,1133,1181,1232,1287,1347,1412,1471,1533,1585,1646,1708,1754,1887,1939,1989,2040,2447,2759,2859,2918,3115,3172,3367,3548,3602,3659,3851,3909,4105,4161,4355,4412,4463,4685,4737,4792,4982,5198,5248,5300,5450,5656,5717,5777,5847,5980,6111,6239,6307,6436,6562,6624,6687,6755,6822,6945,7070,7137,7202,7267,7556,7737,7858,7979,8045,8112,8322,8391,8457,8582,8708,8775,8901,9028,9153,9280,9336,9401,9527,9650,9715,9923,9990,10278,10458,10578,10698,10763,10825,10887,10951,11013,11072,11132,11193,11254,11313,11373,12033,12284,12335,12384,12432,12490,12842,13072,13119,13179,13285,13465,13519,13854,13908,13964,14010,14057,14108,14167,14260,14590,14649,14703,14941,14996,15198,15337,15383,15438,15483,15527,15875,16012,32322,33602,165883,175029,180694,181069,181236,186466,192592,193289,198192,199046,199916,199982,200061,200136,200920,201811,202630,206567,213982,214453,215244,215407,216768,217332,217485,217944,218362,220375,221051,223228,223886,228303,231177,232598,232951,235547,235648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\0acb1fec1e7ba4af001e41f13611abe5\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "637", "startColumns": "4", "startOffsets": "33764", "endColumns": "49", "endOffsets": "33809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f5d9e677a0fad52e699ee987ed2bc3d7\\transformed\\jetified-play-services-games-v2-20.1.2\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "207,379", "endLines": "4,14", "endColumns": "74,8", "endOffsets": "281,781"}, "to": {"startLines": "699,2122", "startColumns": "4,4", "startOffsets": "39047,133632", "endLines": "699,2129", "endColumns": "78,8", "endOffsets": "39121,134034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\38f67d84e234ba470211615c7ce575fe\\transformed\\core-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,2086,2145,2204,2264,2324,2384,2444,2504,2564,2624,2684,2744,2804,2863,2923,2983,3043,3103,3163,3223,3283,3343,3403,3463,3522,3582,3642,3701,3760,3819,3878,3937,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,2140,2199,2259,2319,2379,2439,2499,2559,2619,2679,2739,2799,2858,2918,2978,3038,3098,3158,3218,3278,3338,3398,3458,3517,3577,3637,3696,3755,3814,3873,3932,3991,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "279,378,379,422,423,526,527,528,529,530,531,532,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,606,607,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,644,701,2075,2076,2080,2081,2085,2252,2253,2899,2951,3617,3650,3680,3713", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12495,16895,16967,20039,20108,27094,27164,27232,27304,27374,27435,27509,28366,28427,28488,28550,28614,28676,28737,28805,28905,28965,29031,29104,29173,29230,29282,29797,29869,29945,30010,30069,30128,30188,30248,30308,30368,30428,30488,30548,30608,30668,30728,30787,30847,30907,30967,31027,31087,31147,31207,31267,31327,31387,31446,31506,31566,31625,31684,31743,31802,31861,32216,32251,32625,32680,32743,32798,32856,32914,32975,33038,33095,33146,33196,33257,33314,33380,33414,33449,34189,39179,130238,130355,130556,130666,130867,143480,143552,164748,167258,193866,195597,196597,197279", "endLines": "279,378,379,422,423,526,527,528,529,530,531,532,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,606,607,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,644,701,2075,2079,2080,2084,2085,2252,2253,2904,2960,3649,3670,3712,3718", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "12550,16962,17050,20103,20166,27159,27227,27299,27369,27430,27504,27577,28422,28483,28545,28609,28671,28732,28800,28900,28960,29026,29099,29168,29225,29277,29339,29864,29940,30005,30064,30123,30183,30243,30303,30363,30423,30483,30543,30603,30663,30723,30782,30842,30902,30962,31022,31082,31142,31202,31262,31322,31382,31441,31501,31561,31620,31679,31738,31797,31856,31915,32246,32281,32675,32738,32793,32851,32909,32970,33033,33090,33141,33191,33252,33309,33375,33409,33444,33479,34254,39245,130350,130551,130661,130862,130991,143547,143614,164946,167554,195592,196273,197274,197441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\022132af6bac98e8948adfeadca2b88c\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "604,613,638,3671,3676", "startColumns": "4,4,4,4,4", "startOffsets": "32125,32514,33814,196278,196448", "endLines": "604,613,638,3675,3679", "endColumns": "56,64,63,24,24", "endOffsets": "32177,32574,33873,196443,196592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\0542d31c6571b24e51fa9b0dd291c530\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "367,368,369,370", "startColumns": "4,4,4,4", "startOffsets": "16134,16199,16269,16333", "endColumns": "64,69,63,60", "endOffsets": "16194,16264,16328,16389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd6faf7814b28a0a7c03912d1fdf5c7c\\transformed\\jetified-core-common-2.0.4\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2130", "startColumns": "4", "startOffsets": "134039", "endLines": "2137", "endColumns": "8", "endOffsets": "134444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5914cd0d1f7ce5b2d77c09c643cca61b\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "678", "startColumns": "4", "startOffsets": "36507", "endColumns": "82", "endOffsets": "36585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\854426de3cc1265ebe6fde1d66be3ffb\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "634", "startColumns": "4", "startOffsets": "33607", "endColumns": "42", "endOffsets": "33645"}}]}]}