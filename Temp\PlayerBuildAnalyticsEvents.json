{"player_build_data": [{"type": "il2cppData", "msg": {"build_event_id": "b493a8a7482b45b40b66bb8f69fe2bb3", "node_executed": true, "attribute_total_count_eager_static_constructor": 82, "attribute_total_count_set_option": 0, "attribute_total_count_generate_into_own_cpp_file": 0, "attribute_total_count_ignore_by_deep_profiler": 44, "extra_types_total_count": 0, "option_extra_types_file_count": 0, "option_debug_assembly_name_count": 0, "option_additional_cpp_count": 13, "option_emit_null_checks": true, "option_enable_stacktrace": false, "option_enable_deep_profiler": false, "option_enable_stats": false, "option_enable_array_bounds_check": true, "option_enable_divide_by_zero_check": false, "option_emit_comments": false, "option_disable_generic_sharing": false, "option_maximum_recursive_generic_depth": -1, "option_generic_virtual_method_iterations": -1, "option_code_generation_option": ["EnableInlining"], "option_file_generation_option": [], "option_generics_option": [], "option_feature": [], "option_diagnostic_option": [], "option_convert_to_cpp": true, "option_compile_cpp": false, "option_development_mode": false, "option_enable_debugger": false, "option_generate_usym_file": false, "option_jobs": 8}, "version": 1}, {"type": "playerBuildTimingData", "msg": {"build_event_id": "b493a8a7482b45b40b66bb8f69fe2bb3", "build_player": 253231, "preprocess_player": 690, "produce_player_script_assemblies": 11078, "build_scripts_dlls": 10, "writing_asset_files": 11421, "postprocess_built_player": 216260, "node_summary_table": [{"name": "Csc", "duration": 5079}, {"name": "il2cpp.exe", "duration": 13554}, {"name": "UnityLinker.exe", "duration": 11678}]}, "version": 1}, {"type": "unityLinkerData", "msg": {"build_event_id": "b493a8a7482b45b40b66bb8f69fe2bb3", "node_executed": true, "attribute_marked_count_always_link_assembly": 1, "attribute_swept_count_always_link_assembly": 0, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 80, "attribute_total_count_preserve": 72, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 1035, "attribute_swept_count_required_member": 196, "attribute_total_count_required_member": 1231, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 3008, "attribute_total_count_require_attribute_usages": 2, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 108, "attribute_swept_count_dynamic_dependency": -108, "assembly_counts_total_in": 139, "assembly_counts_link": 46, "assembly_counts_copy": 45, "assembly_counts_delete": 48, "assembly_counts_total_out": 91, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 706, "unrecognized_reflection_access_core_count": 375, "unrecognized_reflection_access_unity_count": 210, "unrecognized_reflection_access_user_count": 121, "recognized_reflection_access_total_count": 127, "recognized_reflection_access_core_count": 82, "recognized_reflection_access_unity_count": 28, "recognized_reflection_access_user_count": 17, "link_xml_total_count": 11, "link_xml_embedded_count": 2, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 1, "link_xml_file_count": 9, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 71, "engine_module_deleted": 34, "engine_module_total_out": 37, "option_rule_set": "Minimal", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": true, "option_unity_root_strategy": "AllNonEngineAndNonClassLibraries", "option_enable_ildump": false}, "version": 1}]}