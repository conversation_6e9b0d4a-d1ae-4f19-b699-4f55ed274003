# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 107ms
  generate-prefab-packages
    exec-prefab 538ms
    [gap of 16ms]
  generate-prefab-packages completed in 563ms
  execute-generate-process
    exec-configure 363ms
    [gap of 113ms]
  execute-generate-process completed in 477ms
  [gap of 28ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 1206ms

