{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "F:\\Match2D\\.utmp\\RelWithDebInfo\\5fj6qx14\\arm64-v8a", "soFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\5fj6qx14\\obj\\arm64-v8a", "soRepublishFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cmake\\release\\obj\\arm64-v8a", "abiPlatformVersion": 23, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "F:\\Match2D\\.utmp", "intermediatesBaseFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates", "intermediatesFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx", "gradleModulePathName": ":unityLibrary", "moduleRootFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary", "moduleBuildFile": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build.gradle", "makeFile": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK", "ndkFolderBeforeSymLinking": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK", "ndkVersion": "27.2.12479018", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle", "sdkFolder": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5652c66f07446d89a89a7ab2b3eed394\\transformed\\jetified-games-activity-3.0.5\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "F:\\Match2D\\.utmp\\RelWithDebInfo\\5fj6qx14\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "5fj6qx14446f6y6k3v6e6hz32u49x6f3nf5o3l3g5s3oo1t2s6m1w5g6g", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.2.\n#   - $NDK is the path to NDK 27.2.12479018.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/unityLibrary/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=23\n-DANDROID_PLATFORM=android-23\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/unityLibrary/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/unityLibrary/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-DCMAKE_FIND_ROOT_PATH=F:/Match2D/.utmp/RelWithDebInfo/$HASH/prefab/$ABI/prefab\n-BF:/Match2D/.utmp/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "configurationArguments": ["-HF:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=23", "-DANDROID_PLATFORM=android-23", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK", "-DCMAKE_ANDROID_NDK=F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK", "-DCMAKE_TOOLCHAIN_FILE=F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\5fj6qx14\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\5fj6qx14\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-DCMAKE_FIND_ROOT_PATH=F:\\Match2D\\.utmp\\RelWithDebInfo\\5fj6qx14\\prefab\\arm64-v8a\\prefab", "-BF:\\Match2D\\.utmp\\RelWithDebInfo\\5fj6qx14\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "stlLibraryFile": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\5fj6qx14"}