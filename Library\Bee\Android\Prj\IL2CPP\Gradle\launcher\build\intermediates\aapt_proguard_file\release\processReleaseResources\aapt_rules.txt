-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.games.internal.v2.appshortcuts.PlayGamesAppShortcutsActivity { <init>(); }
-keep class com.google.android.gms.games.internal.v2.resolution.GamesResolutionActivity { <init>(); }
-keep class com.google.android.gms.games.provider.PlayGamesInitProvider { <init>(); }
-keep class com.google.android.gms.nearby.exposurenotification.WakeUpService { <init>(); }
-keep class com.google.android.play.core.assetpacks.AssetPackExtractionService { <init>(); }
-keep class com.google.android.play.core.assetpacks.ExtractionForegroundService { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.games.bridge.GenericResolutionActivity { <init>(); }
-keep class com.google.games.bridge.NativeBridgeActivity { <init>(); }
-keep class com.ironsource.lifecycle.IronsourceLifecycleProvider { <init>(); }
-keep class com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider { <init>(); }
-keep class com.ironsource.mediationsdk.testSuite.TestSuiteActivity { <init>(); }
-keep class com.ironsource.sdk.controller.ControllerActivity { <init>(); }
-keep class com.ironsource.sdk.controller.InterstitialActivity { <init>(); }
-keep class com.ironsource.sdk.controller.OpenUrlActivity { <init>(); }
-keep class com.unity3d.ads.adplayer.FullScreenWebViewDisplay { <init>(); }
-keep class com.unity3d.player.appui.AppUIGameActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitSoftwareActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitTransparentActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

