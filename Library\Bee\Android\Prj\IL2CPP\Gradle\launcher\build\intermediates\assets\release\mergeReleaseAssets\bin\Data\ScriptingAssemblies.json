{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GraphicsStateCollectionSerializerModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HierarchyCoreModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.IdentifiersModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputForUIModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.InsightsModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.MarshallingModule.dll", "UnityEngine.MultiplayerModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.ShaderVariantAnalyticsModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityConsentModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp.dll", "CFXRDemo.dll", "CFXRRuntime.dll", "CryptoLibrary.dll", "EventFramework.dll", "Google.Play.AppUpdate.dll", "Google.Play.Common.dll", "Google.Play.Core.dll", "Google.Play.Games.dll", "KinoBloom.Runtime.dll", "OwnMatch3.dll", "PrimeTween.Runtime.dll", "UniTask.Addressables.dll", "UniTask.dll", "UniTask.DOTween.dll", "UniTask.Linq.dll", "UniTask.TextMeshPro.dll", "Unity.2D.Animation.Runtime.dll", "Unity.2D.Common.Runtime.dll", "Unity.2D.IK.Runtime.dll", "Unity.2D.PixelPerfect.dll", "Unity.2D.SpriteShape.Runtime.dll", "Unity.2D.Tilemap.Extras.dll", "Unity.AI.Navigation.dll", "Unity.AppUI.dll", "Unity.AppUI.InternalAPIBridge.dll", "Unity.AppUI.MVVM.dll", "Unity.AppUI.Navigation.dll", "Unity.AppUI.Redux.dll", "Unity.AppUI.Undo.dll", "Unity.Burst.dll", "Unity.Collections.dll", "Unity.InferenceEngine.dll", "Unity.InputSystem.dll", "Unity.InputSystem.ForUI.dll", "Unity.InternalAPIEngineBridge.001.dll", "Unity.LevelPlay.dll", "Unity.Mathematics.dll", "Unity.Multiplayer.Center.Common.dll", "Unity.Recorder.Base.dll", "Unity.Recorder.dll", "Unity.Rendering.LightTransport.Runtime.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.Shared.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "Unity.RenderPipelines.GPUDriven.Runtime.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Unity.RenderPipelines.Universal.2D.Runtime.dll", "Unity.RenderPipelines.Universal.Config.Runtime.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.RenderPipelines.Universal.Shaders.dll", "Unity.Services.Core.Analytics.dll", "Unity.Services.Core.Components.dll", "Unity.Services.Core.Configuration.dll", "Unity.Services.Core.Device.dll", "Unity.Services.Core.dll", "Unity.Services.Core.Environments.dll", "Unity.Services.Core.Environments.Internal.dll", "Unity.Services.Core.Internal.dll", "Unity.Services.Core.Networking.dll", "Unity.Services.Core.Registration.dll", "Unity.Services.Core.Scheduler.dll", "Unity.Services.Core.Telemetry.dll", "Unity.Services.Core.Threading.dll", "Unity.TextMeshPro.dll", "Unity.Timeline.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.VisualScripting.Core.dll", "Unity.VisualScripting.Flow.dll", "Unity.VisualScripting.State.dll", "UnityEngine.UI.dll", "ZString.dll", "Unity.Collections.LowLevel.ILSupport.dll", "System.IO.Hashing.dll", "Unity.VisualScripting.Antlr3.Runtime.dll", "Unity.Burst.Unsafe.dll", "System.Runtime.CompilerServices.Unsafe.dll", "Newtonsoft.Json.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}