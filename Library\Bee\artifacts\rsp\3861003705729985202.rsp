--allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IdentifiersModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InsightsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConsentModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/CFXRDemo.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/CFXRRuntime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/CryptoLibrary.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/EventFramework.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.AppUpdate.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Common.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Core.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Games.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/KinoBloom.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/OwnMatch3.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/PrimeTween.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.Addressables.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.DOTween.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.Linq.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.TextMeshPro.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Animation.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.IK.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.PixelPerfect.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Tilemap.Extras.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.LevelPlay.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Analytics.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Components.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Configuration.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Device.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Environments.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Environments.Internal.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Internal.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Networking.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Registration.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Scheduler.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Telemetry.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Threading.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --allowed-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/ZString.dll" --allowed-assembly="F:/Match2D/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll" --allowed-assembly="F:/Match2D/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll" --allowed-assembly="F:/Match2D/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll" --allowed-assembly="F:/Match2D/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll" --allowed-assembly="F:/Match2D/Assets/Plugins/System.Runtime.CompilerServices.Unsafe.dll" --allowed-assembly="F:/Match2D/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.CJK.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.MidEast.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Other.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Rare.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.West.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll" --allowed-assembly="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll" --out="Library/Bee/artifacts/Android/ManagedStripped" --include-link-xml="F:/Match2D/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml" --include-link-xml="F:/Match2D/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml" --include-link-xml="F:/Match2D/Library/InputSystem/AndroidLink.xml" --include-link-xml="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/AndroidNativeLink.xml" --include-directory="F:/Match2D/Library/Bee/PlayerScriptAssemblies" --include-directory="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed" --include-directory="F:/Match2D/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport" --include-directory="F:/Match2D/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing" --include-directory="F:/Match2D/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc" --include-directory="F:/Match2D/Library/PackageCache/com.unity.burst@6aff1dd08a0c" --include-directory="F:/Match2D/Assets/Plugins" --include-directory="F:/Match2D/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT" --include-directory="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux" --include-directory="F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades" --profiler-report --profiler-output-file="F:/Match2D/Library/Bee/artifacts/unitylinker_xy1a.traceevents" --dotnetprofile=unityaot-linux --dotnetruntime=Il2Cpp --platform=Android --use-editor-options --enable-engine-module-stripping --engine-modules-asset-file="F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/modules.asset" --editor-data-file="F:/Match2D/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/CFXRDemo.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/CFXRRuntime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/CryptoLibrary.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/EventFramework.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.AppUpdate.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Common.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Core.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Games.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/KinoBloom.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/OwnMatch3.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.Addressables.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.DOTween.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.Linq.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.TextMeshPro.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Animation.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.IK.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Tilemap.Extras.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.LevelPlay.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --include-unity-root-assembly="F:/Match2D/Library/Bee/PlayerScriptAssemblies/ZString.dll" --print-command-line --enable-analytics