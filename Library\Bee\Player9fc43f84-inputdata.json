{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\il2cpp", "UnityLinkerPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\il2cpp/build/deploy/UnityLinker.exe", "Il2CppPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\il2cpp/build/deploy/il2cpp.exe", "NetCoreRunPath": "F:/Unity Installs/6000.2.0b9/Editor/Data\\Tools\\netcorerun\\netcorerun.exe", "DotNetExe": "F:/Unity Installs/6000.2.0b9/Editor/Data/NetCoreRuntime/dotnet.exe", "EditorContentsPath": "F:/Unity Installs/6000.2.0b9/Editor/Data", "Packages": [{"Name": "com.kyrylokuzyk.primetween", "ResolvedPath": "Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699"}, {"Name": "com.unity.2d.animation", "ResolvedPath": "Library/PackageCache/com.unity.2d.animation@34e0443c58ed"}, {"Name": "com.unity.2d.common", "ResolvedPath": "Library/PackageCache/com.unity.2d.common@dd402daace1b"}, {"Name": "com.unity.2d.enhancers", "ResolvedPath": "Library/PackageCache/com.unity.2d.enhancers@1df0eb7756ab"}, {"Name": "com.unity.2d.psdimporter", "ResolvedPath": "Library/PackageCache/com.unity.2d.psdimporter@0adcab25a8fd"}, {"Name": "com.unity.2d.sprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.sprite@28296e5d02fb"}, {"Name": "com.unity.2d.spriteshape", "ResolvedPath": "Library/PackageCache/com.unity.2d.spriteshape@1d246726c231"}, {"Name": "com.unity.2d.tilemap", "ResolvedPath": "Library/PackageCache/com.unity.2d.tilemap@089d78c497f7"}, {"Name": "com.unity.2d.tilemap.extras", "ResolvedPath": "Library/PackageCache/com.unity.2d.tilemap.extras@2338d989ff2a"}, {"Name": "com.unity.ai.inference", "ResolvedPath": "Library/PackageCache/com.unity.ai.inference@803814f81708"}, {"Name": "com.unity.ai.navigation", "ResolvedPath": "Library/PackageCache/com.unity.ai.navigation@eb5635ad590d"}, {"Name": "com.unity.bindings.openimageio", "ResolvedPath": "Library/PackageCache/com.unity.bindings.openimageio@3229d2aa5c76"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst@6aff1dd08a0c"}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f"}, {"Name": "com.unity.collections", "ResolvedPath": "Library/PackageCache/com.unity.collections@d49facba0036"}, {"Name": "com.unity.dt.app-ui", "ResolvedPath": "Library/PackageCache/com.unity.dt.app-ui@5d6dc8bfd8d9"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@031a54704bff"}, {"Name": "com.unity.feature.2d", "ResolvedPath": "Library/PackageCache/com.unity.feature.2d@dd1ea8910f12"}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider@4d374c7eb6db"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13"}, {"Name": "com.unity.inputsystem", "ResolvedPath": "Library/PackageCache/com.unity.inputsystem@7fe8299111a7"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@8017b507cc74"}, {"Name": "com.unity.mobile.android-logcat", "ResolvedPath": "Library/PackageCache/com.unity.mobile.android-logcat@0ddcd2133dc3"}, {"Name": "com.unity.multiplayer.center", "ResolvedPath": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546"}, {"Name": "com.unity.nuget.mono-cecil", "ResolvedPath": "Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb"}, {"Name": "com.unity.nuget.newtonsoft-json", "ResolvedPath": "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0"}, {"Name": "com.unity.recorder", "ResolvedPath": "Library/PackageCache/com.unity.recorder@979a3db2a781"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core@a0d4832bc90e"}, {"Name": "com.unity.render-pipelines.universal", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal@96f07f2db2b6"}, {"Name": "com.unity.render-pipelines.universal-config", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal-config@8dc1aab4af1d"}, {"Name": "com.unity.rendering.light-transport", "ResolvedPath": "Library/PackageCache/com.unity.rendering.light-transport@2c9279f90d7c"}, {"Name": "com.unity.services.levelplay", "ResolvedPath": "Library/PackageCache/com.unity.services.levelplay@77e2031f7b0e"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph@d54196c2b36d"}, {"Name": "com.unity.sysroot", "ResolvedPath": "Library/PackageCache/com.unity.sysroot@7bbbb9339c14"}, {"Name": "com.unity.sysroot.linux-x86_64", "ResolvedPath": "Library/PackageCache/com.unity.sysroot.linux-x86_64@1998d1c7730e"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@63d98114e53c"}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline@c58b4ee65782"}, {"Name": "com.unity.toolchain.win-x86_64-linux-x86_64", "ResolvedPath": "Library/PackageCache/com.unity.toolchain.win-x86_64-linux-x86_64@426618737602"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@ecde93d85e93"}, {"Name": "com.unity.visualeffectgraph", "ResolvedPath": "Library/PackageCache/com.unity.visualeffectgraph@d6a559b25a67"}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting@6279e2b7c485"}, {"Name": "com.unity.modules.accessibility", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio"}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.cloth"}, {"Name": "com.unity.modules.director", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.director"}, {"Name": "com.unity.modules.hierarchycore", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture"}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.subsystems"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements"}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.umbra"}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unityanalytics"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww"}, {"Name": "com.unity.modules.video", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.video"}, {"Name": "com.unity.modules.wind", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.wind"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher@1e17ce91558d"}, {"Name": "com.unity.services.core", "ResolvedPath": "Library/PackageCache/com.unity.services.core@9e81a5e38245"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain"}, {"Name": "com.unity.2d.pixel-perfect", "ResolvedPath": "Library/PackageCache/com.unity.2d.pixel-perfect@2f2037a56bf7"}, {"Name": "com.unity.2d.aseprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.aseprite@f6e7e126ac6d"}, {"Name": "com.unity.test-framework.performance", "ResolvedPath": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed"}, {"Name": "com.unity.modules.ai", "ResolvedPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.ai"}, {"Name": "com.unity.ai.generators", "ResolvedPath": "Library/PackageCache/com.unity.ai.generators@bf6bfb1a644e"}, {"Name": "com.unity.settings-manager", "ResolvedPath": "Library/PackageCache/com.unity.settings-manager@41738c275190"}, {"Name": "com.unity.ai.toolkit", "ResolvedPath": "Library/PackageCache/com.unity.ai.toolkit@97783faabe3b"}], "UnityVersion": "6000.2.0b9", "UnityVersionNumeric": {"Release": 6000, "Major": 2, "Minor": 0}, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "unity-ilpp-d51f94e36cd862d0fa918b99294a8ef5"}, "PlayerBuildProgramLibrary.Data.PlayerBuildConfig": {"DestinationPath": "F:\\Match2D\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle", "StagingArea": "Temp/StagingArea", "DataFolder": "Library/PlayerDataCache/Android/Data", "CompanyName": "PhantomTeam", "ProductName": "Puffland Adventure", "PlayerPackage": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer", "ApplicationIdentifier": "com.PhantomTeam.PufflandAdventure", "Architecture": "", "ScriptingBackend": "IL2CPP", "NoGUID": false, "InstallIntoBuildsFolder": false, "GenerateIdeProject": false, "Development": false, "UseNewInputSystem": true, "GenerateNativePluginsForAssembliesSettings": {"HasCallback": true, "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "AdditionalInputFiles": ["ProjectSettings/BurstAotSettings_Android.json", "Library/BurstCache/AotSettings_Android.hash", "F:/Match2D/Library/PackageCache/com.unity.burst@6aff1dd08a0c/.Runtime\\bcl.exe"]}, "Services": {"EnableUnityConnect": false, "EnablePerformanceReporting": false, "EnableAnalytics": false, "EnableCrashReporting": false, "EnableInsights": false}, "ManagedAssemblies": ["F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IdentifiersModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InsightsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConsentModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/CFXRDemo.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/CFXRRuntime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/CryptoLibrary.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/EventFramework.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.AppUpdate.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Common.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Core.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Google.Play.Games.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/KinoBloom.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/OwnMatch3.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/PrimeTween.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.Addressables.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.DOTween.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.Linq.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/UniTask.TextMeshPro.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Animation.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.IK.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.PixelPerfect.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.2D.Tilemap.Extras.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.LevelPlay.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Analytics.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Components.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Configuration.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Device.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Environments.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Environments.Internal.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Internal.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Networking.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Registration.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Scheduler.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Telemetry.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Services.Core.Threading.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll", "F:/Match2D/Library/Bee/PlayerScriptAssemblies/ZString.dll", "F:/Match2D/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "F:/Match2D/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll", "F:/Match2D/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll", "F:/Match2D/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll", "F:/Match2D/Assets/Plugins/System.Runtime.CompilerServices.Unsafe.dll", "F:/Match2D/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.CJK.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.MidEast.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Other.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Rare.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.West.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll", "F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll"], "StreamingAssetsFiles": [{"File": "Library/com.unity.services.core/UnityServicesProjectConfiguration.json", "RelativePath": "UnityServicesProjectConfiguration.json"}]}, "PlayerBuildProgramLibrary.Data.PluginsData": {"Plugins": [{"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/BannerLevelPlayCallbacksWrapper.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Assets/GooglePlayPlugins/com.google.play.appupdate/Runtime/Plugins/com.google.play.appupdate.metadata.jar", "DestinationPath": "unityLibrary\\libs\\com.google.play.appupdate.metadata.jar", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMBannerAdView.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMUtilities.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayBannerListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnityLevelPlayBannerListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMInterstitial/LPMInterstitialAdCallbacksWrapper.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayRewardedVideoListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnityLevelPlayRewardedVideoListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/iOSBridge.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/RewardedAd.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\RewardedAd.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityBannerAdListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\IUnityBannerAdListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnitySegmentListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnitySegmentListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/InterstitialAd.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\InterstitialAd.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityImpressionDataListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnityImpressionDataListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/AndroidBridgeConstants.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\AndroidBridgeConstants.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMInterstitial/LPMInterstitialAd.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IronSource.androidlib", "DestinationPath": "unityLibrary\\IronSource.androidlib", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/BannerAd.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\BannerAd.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityRewardedAdListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\IUnityRewardedAdListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.dt.app-ui/Runtime/Core/Platform/Android/Plugins/Android/HapticFeedback.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\unity3d\\player\\appui\\HapticFeedback.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayRewardedVideoWrapper.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\LevelPlayRewardedVideoWrapper.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMRewardedAd/LPMRewardedAdCallbacksWrapper.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMInitializer.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/AndroidBridge.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\AndroidBridge.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Assets/Plugins/Android/GooglePlayGamesManifest.androidlib", "DestinationPath": "unityLibrary\\GooglePlayGamesManifest.androidlib", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityLevelPlayInitListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\IUnityLevelPlayInitListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/InterstitialLevelPlayCallbacksWrapper.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMRewardedAd/LPMRewardedAd.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityInitializationListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnityInitializationListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/RewardedVideoLevelPlayCallbacksWrapper.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayInterstitialListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnityLevelPlayInterstitialListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/BannerUtils.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\BannerUtils.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityInterstitialAdListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\IUnityInterstitialAdListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayUtils.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\LevelPlayUtils.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayBridge.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\LevelPlayBridge.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMBannerAdViewCallbacksWrapper.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMBannerPosition.h", "DestinationPath": "", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/AndroidBridgeUtilities.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\AndroidBridgeUtilities.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayRewardedVideoManualListener.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\UnityLevelPlayRewardedVideoManualListener.java", "Architecture": "", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayInterstitialWrapper.java", "DestinationPath": "unityLibrary\\src\\main\\java\\com\\ironsource\\unity\\androidbridge\\LevelPlayInterstitialWrapper.java", "Architecture": "", "AddToEmbeddedBinaries": false}]}, "PlayerBuildProgramLibrary.Data.LinkerConfig": {"LinkXmlFiles": ["F:/Match2D/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml", "F:/Match2D/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml", "F:/Match2D/Library/InputSystem/AndroidLink.xml"], "AssembliesToProcess": ["Assembly-CSharp.dll", "CFXRDemo.dll", "CFXRRuntime.dll", "CryptoLibrary.dll", "EventFramework.dll", "Google.Play.AppUpdate.dll", "Google.Play.Common.dll", "Google.Play.Core.dll", "Google.Play.Games.dll", "KinoBloom.Runtime.dll", "OwnMatch3.dll", "UniTask.Addressables.dll", "UniTask.dll", "UniTask.DOTween.dll", "UniTask.Linq.dll", "UniTask.TextMeshPro.dll", "Unity.2D.Animation.Runtime.dll", "Unity.2D.IK.Runtime.dll", "Unity.2D.SpriteShape.Runtime.dll", "Unity.2D.Tilemap.Extras.dll", "Unity.AppUI.dll", "Unity.InputSystem.dll", "Unity.LevelPlay.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.RenderPipelines.Universal.2D.Runtime.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.VisualEffectGraph.Runtime.dll", "UnityEngine.UI.dll", "ZString.dll"], "EditorToLinkerData": "F:/Match2D/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json", "Runtime": "il2cpp", "Profile": "unityaot-linux", "Ruleset": "Minimal", "ModulesAssetPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/modules.asset", "AdditionalArgs": [], "AllowDebugging": false, "PerformEngineStripping": true}, "PlayerBuildProgramLibrary.Data.Il2CppConfig": {"EnableDeepProfilingSupport": false, "EnableFullGenericSharing": false, "Profile": "unityaot-linux", "IDEProjectDefines": ["ALL_INTERIOR_POINTERS=1", "GC_GCJ_SUPPORT=1", "JAVA_FINALIZATION=1", "NO_EXECUTE_PERMISSION=1", "GC_NO_THREADS_DISCOVERY=1", "IGNORE_DYNAMIC_LOADING=1", "GC_DONT_REGISTER_MAIN_STATIC_DATA=1", "GC_VERSION_MAJOR=7", "GC_VERSION_MINOR=7", "GC_VERSION_MICRO=0", "GC_THREADS=1", "USE_MMAP=1", "USE_MUNMAP=1", "NET_4_0=1", "UNITY_AOT=1", "NET_STANDARD_2_0=1", "NET_UNITY_4_8=1", "NET_STANDARD=1", "IL2CPP_ENABLE_WRITE_BARRIERS=1", "IL2CPP_INCREMENTAL_TIME_SLICE=3"], "ConfigurationName": "Release", "GcWBarrierValidation": false, "GcIncremental": true, "AdditionalCppFiles": ["Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/BannerLevelPlayCallbacksWrapper.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMBannerAdView.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMUtilities.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMInterstitial/LPMInterstitialAdCallbacksWrapper.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/iOSBridge.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMInterstitial/LPMInterstitialAd.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMRewardedAd/LPMRewardedAdCallbacksWrapper.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMInitializer.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/InterstitialLevelPlayCallbacksWrapper.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMRewardedAd/LPMRewardedAd.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/RewardedVideoLevelPlayCallbacksWrapper.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMBannerAdViewCallbacksWrapper.h", "Packages/com.unity.services.levelplay/Runtime/Plugins/iOS/LPMBannerPosition.h"], "AdditionalArgs": [], "AdditionalLibraries": [], "AdditionalDefines": [], "AdditionalIncludeDirectories": [], "AdditionalLinkDirectories": [], "CreateSymbolFiles": true, "AllowDebugging": false, "RelativeDataPath": "Data", "GenerateUsymFile": false, "UsymtoolPath": "F:/Unity Installs/6000.2.0b9/Editor/Data\\Tools\\usymtool.exe"}, "AndroidPlayerBuildProgram.Data.AndroidPlayerBuildConfiguration": {"GradleProjectCreateInfo": {"EnvironmentVariableInputs": ["UNITY_THISISABUILDMACHINE:"], "HostPlatform": "Windows", "ApplicationType": "AppBundle", "BuildType": "Release", "AndroidSDKPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK", "AndroidNDKPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK", "AndroidJavaPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\OpenJDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "8.9", "ProjectFiles": {"UnityLibraryBuildGradle": {"SourcePath": "Assets/Plugins/Android\\mainTemplate.gradle", "RelativeDestinationPath": "unityLibrary/build.gradle", "CanBeModifiedByUser": true}, "LauncherBuildGradle": {"RelativeDestinationPath": "launcher/build.gradle", "CanBeModifiedByUser": true}, "LauncherSetupUnitySymbolsGradle": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\launcher/setupSymbols.gradle", "RelativeDestinationPath": "launcher/setupSymbols.gradle", "CanBeModifiedByUser": false}, "SharedKeepUnitySymbolsGradle": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\shared/keepUnitySymbols.gradle", "RelativeDestinationPath": "shared/keepUnitySymbols.gradle", "CanBeModifiedByUser": false}, "SharedCommonGradle": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\GradleTemplates\\shared/common.gradle", "RelativeDestinationPath": "shared/common.gradle", "CanBeModifiedByUser": false}, "ProjectLevelBuildGradle": {"RelativeDestinationPath": "build.gradle", "CanBeModifiedByUser": true}, "GradleProperties": {"SourcePath": "Assets/Plugins/Android\\gradleTemplate.properties", "RelativeDestinationPath": "gradle.properties", "CanBeModifiedByUser": true}, "UnityProguard": {"SourcePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools\\UnityProGuardTemplate.txt", "RelativeDestinationPath": "unityLibrary/proguard-unity.txt", "CanBeModifiedByUser": true}, "ProguardUser": {"SourcePath": "Assets/Plugins/Android\\proguard-user.txt", "RelativeDestinationPath": "unityLibrary/proguard-user.txt", "CanBeModifiedByUser": true}, "GradleSettings": {"SourcePath": "Assets/Plugins/Android\\settingsTemplate.gradle", "RelativeDestinationPath": "settings.gradle", "CanBeModifiedByUser": true}, "LocalProperties": {"RelativeDestinationPath": "local.properties", "CanBeModifiedByUser": true}}, "AdditionalUserInputs": ["F:\\Match2D\\Library\\ScriptAssemblies\\Unity.AppUI.Editor.dll"], "AdditionalUserOutputs": {"AdditionalManifests": [], "AdditionalBuildGradleFiles": [], "AdditionalGradleSettings": [], "AdditionalGradleProperties": [], "AdditionalFilesWithContents": []}, "UserCopyData": {"FilesToCopy": [{"SourceFile": "F:\\Match2D\\Library\\PackageCache\\com.unity.dt.app-ui@5d6dc8bfd8d9\\Runtime/Core/Platform/Android/Plugins/Android/AppUIGameActivity.java", "DestinationFile": "unityLibrary/src/main/java/com/unity3d/player/appui/AppUIGameActivity.java"}], "DirectoriesToCopy": []}, "AdditionalUserData": [], "BuildTools": "34.0.0", "TargetSDKVersion": 36, "MinSDKVersion": 23, "PackageName": "com.PhantomTeam.PufflandAdventure", "Architectures": "ARM64", "BuildApkPerCpuArchitecture": false, "DebugSymbols": {"Level": "None", "Format": "5"}, "VersionCode": 11, "VersionName": "0.2.01", "Minify": 3, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": [".json"]}, "UseCustomKeystore": true, "KeystorePath": "F:/Match2D/match3-release-key.keystore", "KeystoreName": "match3-release-key.keystore", "KeystorePassword": "kapusta12", "KeystoreAliasName": "match3_key", "KeystoreAliasPassword": "kapusta12", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": [{"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IronSource.androidlib", "Dependee": "UnityLibrary", "IsAffectedBySymlinkSources": false}, {"Path": "Assets/Plugins/Android/GooglePlayGamesManifest.androidlib", "Dependee": "UnityLibrary", "IsAffectedBySymlinkSources": false}], "AARFiles": [], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerGameActivity.java"], "JavaSourcePaths": [{"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayBannerListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayRewardedVideoListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/RewardedAd.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityBannerAdListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnitySegmentListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/InterstitialAd.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityImpressionDataListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/AndroidBridgeConstants.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/BannerAd.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityRewardedAdListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.dt.app-ui/Runtime/Core/Platform/Android/Plugins/Android/HapticFeedback.java", "PackageName": "com.unity3d.player.appui"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayRewardedVideoWrapper.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/AndroidBridge.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityLevelPlayInitListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityInitializationListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayInterstitialListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/BannerUtils.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/IUnityInterstitialAdListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayUtils.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayBridge.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/AndroidBridgeUtilities.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/UnityLevelPlayRewardedVideoManualListener.java", "PackageName": "com.ironsource.unity.androidbridge"}, {"Path": "Packages/com.unity.services.levelplay/Runtime/Plugins/Android/LevelPlayInterstitialWrapper.java", "PackageName": "com.ironsource.unity.androidbridge"}], "KotlinSourcePaths": [], "PlayerPackage": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "F:/Match2D", "OverrideCMakeIntermdiateDirectory": true, "Dependencies": ["com.google.android.play:asset-delivery:2.1.0"], "ApplicationEntry": "GameActivity", "JarFiles": ["classes.jar"], "UseOptimizedFramePacing": false, "ReportGooglePlayAppDependencies": true, "UnityVersion": "6000.2.0b9"}, "Architectures": "ARM64", "BuildType": "Release", "LinkTimeOptimization": "None", "BuildSystem": "<PERSON><PERSON><PERSON>", "ScriptingImplementation": "IL2CPP", "DebugSymbol": {"Level": "None", "Format": "5"}, "ApplicationSplitMode": "UsingAssetPacks", "TargetTextureCompression": false, "GradleResourcesInformation": {"TargetSDKVersion": 36, "RoundIconsAvailable": true, "RoundIconsSupported": true, "AdaptiveIconsSupported": true, "AdaptiveIconsAvailable": true}, "PreloadedJavaClasses": [], "CustomAssetPacks": [], "DependenciesProtobufForPlayStoreReporting": [{"version": "1.3.2", "source": "LocalTarball", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.kyrylokuzyk.primetween@ab1c59930699", "name": "com.kyrylokuzyk.primetween", "dependencies": [], "location": ""}, {"version": "12.0.2", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.animation@34e0443c58ed", "name": "com.unity.2d.animation", "dependencies": [{"version": "11.0.1", "name": "com.unity.2d.common"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}], "location": "https://packages.unity.com"}, {"version": "11.0.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.common@dd402daace1b", "name": "com.unity.2d.common", "dependencies": [{"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.enhancers@1df0eb7756ab", "name": "com.unity.2d.enhancers", "dependencies": [{"version": "11.0.1", "name": "com.unity.2d.common"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0-pre.12", "name": "com.unity.ai.generators"}, {"version": "1.0.0-pre.12", "name": "com.unity.ai.toolkit"}, {"version": "3.2.1", "name": "com.unity.nuget.newtonsoft-json"}, {"version": "2.1.0", "name": "com.unity.settings-manager"}], "location": "https://packages.unity.com"}, {"version": "11.0.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.psdimporter@0adcab25a8fd", "name": "com.unity.2d.psdimporter", "dependencies": [{"version": "11.0.1", "name": "com.unity.2d.common"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.2d.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.physics2d"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.sprite@28296e5d02fb", "name": "com.unity.2d.sprite", "dependencies": [], "location": ""}, {"version": "12.0.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.spriteshape@1d246726c231", "name": "com.unity.2d.spriteshape", "dependencies": [{"version": "11.0.1", "name": "com.unity.2d.common"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.physics2d"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.tilemap@089d78c497f7", "name": "com.unity.2d.tilemap", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.physics2d"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": ""}, {"version": "5.0.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.tilemap.extras@2338d989ff2a", "name": "com.unity.2d.tilemap.extras", "dependencies": [{"version": "1.0.0", "name": "com.unity.2d.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.physics2d"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": "https://packages.unity.com"}, {"version": "2.2.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ai.inference@803814f81708", "name": "com.unity.ai.inference", "dependencies": [{"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.imageconversion"}], "location": "https://packages.unity.com"}, {"version": "2.0.8", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ai.navigation@eb5635ad590d", "name": "com.unity.ai.navigation", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.ai"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.bindings.openimageio@3229d2aa5c76", "name": "com.unity.bindings.openimageio", "dependencies": [{"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}], "location": "https://packages.unity.com"}, {"version": "1.8.23", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.burst@6aff1dd08a0c", "name": "com.unity.burst", "dependencies": [{"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": "https://packages.unity.com"}, {"version": "2.8.2", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f", "name": "com.unity.collab-proxy", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "2.5.7", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.collections@d49facba0036", "name": "com.unity.collections", "dependencies": [{"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}], "location": "https://packages.unity.com"}, {"version": "2.0.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.dt.app-ui@5d6dc8bfd8d9", "name": "com.unity.dt.app-ui", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.androidjni"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.screencapture"}, {"version": "1.0.0", "name": "com.unity.modules.imageconversion"}], "location": "https://packages.unity.com"}, {"version": "2.0.5", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ext.nunit@031a54704bff", "name": "com.unity.ext.nunit", "dependencies": [], "location": ""}, {"version": "2.0.1", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.feature.2d@dd1ea8910f12", "name": "com.unity.feature.2d", "dependencies": [{"version": "12.0.2", "name": "com.unity.2d.animation"}, {"version": "11.0.1", "name": "com.unity.2d.common"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "5.1.0", "name": "com.unity.2d.pixel-perfect"}, {"version": "11.0.1", "name": "com.unity.2d.psdimporter"}, {"version": "1.0.0", "name": "com.unity.2d.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.physics2d"}, {"version": "12.0.1", "name": "com.unity.2d.spriteshape"}, {"version": "5.0.1", "name": "com.unity.2d.tilemap.extras"}, {"version": "2.0.1", "name": "com.unity.2d.aseprite"}], "location": ""}, {"version": "3.0.36", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db", "name": "com.unity.ide.rider", "dependencies": [{"version": "2.0.5", "name": "com.unity.ext.nunit"}], "location": "https://packages.unity.com"}, {"version": "2.0.23", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "name": "com.unity.ide.visualstudio", "dependencies": [{"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": "https://packages.unity.com"}, {"version": "1.14.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7", "name": "com.unity.inputsystem", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": "https://packages.unity.com"}, {"version": "1.3.2", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "name": "com.unity.mathematics", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "1.4.5", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.mobile.android-logcat@0ddcd2133dc3", "name": "com.unity.mobile.android-logcat", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546", "name": "com.unity.multiplayer.center", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": ""}, {"version": "1.11.5", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d78732e851eb", "name": "com.unity.nuget.mono-cecil", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "3.2.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@74deb55db2a0", "name": "com.unity.nuget.newtonsoft-json", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "5.1.2", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.recorder@979a3db2a781", "name": "com.unity.recorder", "dependencies": [{"version": "1.8.7", "name": "com.unity.timeline"}, {"version": "1.0.0", "name": "com.unity.modules.audio"}, {"version": "1.0.0", "name": "com.unity.modules.director"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.particlesystem"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.bindings.openimageio"}], "location": "https://packages.unity.com"}, {"version": "17.2.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.render-pipelines.core@a0d4832bc90e", "name": "com.unity.render-pipelines.core", "dependencies": [{"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "2.0.0", "name": "com.unity.ugui"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.terrain"}, {"version": "1.0.1", "name": "com.unity.rendering.light-transport"}], "location": ""}, {"version": "17.2.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.render-pipelines.universal@96f07f2db2b6", "name": "com.unity.render-pipelines.universal", "dependencies": [{"version": "17.2.0", "name": "com.unity.render-pipelines.core"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "2.0.0", "name": "com.unity.ugui"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.terrain"}, {"version": "1.0.1", "name": "com.unity.rendering.light-transport"}, {"version": "17.2.0", "name": "com.unity.shadergraph"}, {"version": "4.9.3", "name": "com.unity.searcher"}, {"version": "17.0.3", "name": "com.unity.render-pipelines.universal-config"}], "location": ""}, {"version": "17.0.3", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.render-pipelines.universal-config@8dc1aab4af1d", "name": "com.unity.render-pipelines.universal-config", "dependencies": [{"version": "17.2.0", "name": "com.unity.render-pipelines.core"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "2.0.0", "name": "com.unity.ugui"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.terrain"}, {"version": "1.0.1", "name": "com.unity.rendering.light-transport"}], "location": ""}, {"version": "1.0.1", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.rendering.light-transport@2c9279f90d7c", "name": "com.unity.rendering.light-transport", "dependencies": [{"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.terrain"}], "location": ""}, {"version": "8.10.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.services.levelplay@77e2031f7b0e", "name": "com.unity.services.levelplay", "dependencies": [{"version": "1.14.0", "name": "com.unity.services.core"}, {"version": "1.0.0", "name": "com.unity.modules.androidjni"}, {"version": "3.2.1", "name": "com.unity.nuget.newtonsoft-json"}, {"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}], "location": "https://packages.unity.com"}, {"version": "17.2.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.shadergraph@d54196c2b36d", "name": "com.unity.shadergraph", "dependencies": [{"version": "17.2.0", "name": "com.unity.render-pipelines.core"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "2.0.0", "name": "com.unity.ugui"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.terrain"}, {"version": "1.0.1", "name": "com.unity.rendering.light-transport"}, {"version": "4.9.3", "name": "com.unity.searcher"}], "location": ""}, {"version": "2.0.10", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.sysroot@7bbbb9339c14", "name": "com.unity.sysroot", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "2.0.9", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.sysroot.linux-x86_64@1998d1c7730e", "name": "com.unity.sysroot.linux-x86_64", "dependencies": [{"version": "2.0.10", "name": "com.unity.sysroot"}], "location": "https://packages.unity.com"}, {"version": "1.5.1", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.test-framework@63d98114e53c", "name": "com.unity.test-framework", "dependencies": [{"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": ""}, {"version": "1.8.7", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "name": "com.unity.timeline", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.audio"}, {"version": "1.0.0", "name": "com.unity.modules.director"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.particlesystem"}], "location": "https://packages.unity.com"}, {"version": "2.0.10", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.toolchain.win-x86_64-linux-x86_64@426618737602", "name": "com.unity.toolchain.win-x86_64-linux-x86_64", "dependencies": [{"version": "2.0.10", "name": "com.unity.sysroot"}, {"version": "2.0.9", "name": "com.unity.sysroot.linux-x86_64"}], "location": "https://packages.unity.com"}, {"version": "2.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ugui@ecde93d85e93", "name": "com.unity.ugui", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}], "location": ""}, {"version": "17.2.0", "source": "BuiltIn", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.visualeffectgraph@d6a559b25a67", "name": "com.unity.visualeffectgraph", "dependencies": [{"version": "17.2.0", "name": "com.unity.shadergraph"}, {"version": "17.2.0", "name": "com.unity.render-pipelines.core"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "2.0.0", "name": "com.unity.ugui"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "2.5.7", "name": "com.unity.collections"}, {"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.11.5", "name": "com.unity.nuget.mono-cecil"}, {"version": "3.1.0", "name": "com.unity.test-framework.performance"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.modules.terrain"}, {"version": "1.0.1", "name": "com.unity.rendering.light-transport"}, {"version": "4.9.3", "name": "com.unity.searcher"}], "location": ""}, {"version": "1.9.7", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485", "name": "com.unity.visualscripting", "dependencies": [{"version": "2.0.0", "name": "com.unity.ugui"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "name": "com.unity.modules.accessibility", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "name": "com.unity.modules.androidjni", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "name": "com.unity.modules.animation", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "name": "com.unity.modules.assetbundle", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "name": "com.unity.modules.audio", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "name": "com.unity.modules.cloth", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "name": "com.unity.modules.director", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.audio"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "name": "com.unity.modules.hierarchycore", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "name": "com.unity.modules.imageconversion", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "name": "com.unity.modules.imgui", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "name": "com.unity.modules.jsonserialize", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "name": "com.unity.modules.particlesystem", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "name": "com.unity.modules.physics", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "name": "com.unity.modules.physics2d", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "name": "com.unity.modules.screencapture", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.imageconversion"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "name": "com.unity.modules.subsystems", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "name": "com.unity.modules.tilemap", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.physics2d"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "name": "com.unity.modules.ui", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "name": "com.unity.modules.uielements", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "name": "com.unity.modules.umbra", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "name": "com.unity.modules.unityanalytics", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "name": "com.unity.modules.unitywebrequest", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "name": "com.unity.modules.unitywebrequestassetbundle", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.assetbundle"}, {"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "name": "com.unity.modules.unitywebrequestaudio", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}, {"version": "1.0.0", "name": "com.unity.modules.audio"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "name": "com.unity.modules.unitywebrequesttexture", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}, {"version": "1.0.0", "name": "com.unity.modules.imageconversion"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "name": "com.unity.modules.unitywebrequestwww", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}, {"version": "1.0.0", "name": "com.unity.modules.unitywebrequestassetbundle"}, {"version": "1.0.0", "name": "com.unity.modules.assetbundle"}, {"version": "1.0.0", "name": "com.unity.modules.unitywebrequestaudio"}, {"version": "1.0.0", "name": "com.unity.modules.audio"}, {"version": "1.0.0", "name": "com.unity.modules.imageconversion"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "name": "com.unity.modules.video", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.audio"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}], "location": ""}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "name": "com.unity.modules.wind", "dependencies": [], "location": ""}, {"version": "4.9.3", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.searcher@1e17ce91558d", "name": "com.unity.searcher", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "1.14.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.services.core@9e81a5e38245", "name": "com.unity.services.core", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.androidjni"}, {"version": "3.2.1", "name": "com.unity.nuget.newtonsoft-json"}, {"version": "1.0.0", "name": "com.unity.modules.unitywebrequest"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "name": "com.unity.modules.terrain", "dependencies": [], "location": ""}, {"version": "5.1.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.pixel-perfect@2f2037a56bf7", "name": "com.unity.2d.pixel-perfect", "dependencies": [{"version": "1.0.0", "name": "com.unity.modules.imgui"}], "location": "https://packages.unity.com"}, {"version": "2.0.1", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.2d.aseprite@f6e7e126ac6d", "name": "com.unity.2d.aseprite", "dependencies": [{"version": "11.0.1", "name": "com.unity.2d.common"}, {"version": "1.8.23", "name": "com.unity.burst"}, {"version": "1.3.2", "name": "com.unity.mathematics"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}, {"version": "1.0.0", "name": "com.unity.2d.sprite"}, {"version": "1.0.0", "name": "com.unity.modules.animation"}, {"version": "1.0.0", "name": "com.unity.modules.uielements"}, {"version": "1.0.0", "name": "com.unity.modules.ui"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.hierarchycore"}, {"version": "1.0.0", "name": "com.unity.modules.physics"}, {"version": "1.0.0", "name": "com.unity.2d.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.tilemap"}, {"version": "1.0.0", "name": "com.unity.modules.physics2d"}], "location": "https://packages.unity.com"}, {"version": "3.1.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed", "name": "com.unity.test-framework.performance", "dependencies": [{"version": "1.5.1", "name": "com.unity.test-framework"}, {"version": "2.0.5", "name": "com.unity.ext.nunit"}, {"version": "1.0.0", "name": "com.unity.modules.imgui"}, {"version": "1.0.0", "name": "com.unity.modules.jsonserialize"}], "location": "https://packages.unity.com"}, {"version": "1.0.0", "source": "BuiltIn", "resolvedPath": "F:\\Unity Installs\\6000.2.0b9\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "name": "com.unity.modules.ai", "dependencies": [], "location": ""}, {"version": "1.0.0-pre.12", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ai.generators@bf6bfb1a644e", "name": "com.unity.ai.generators", "dependencies": [{"version": "1.0.0-pre.12", "name": "com.unity.ai.toolkit"}, {"version": "3.2.1", "name": "com.unity.nuget.newtonsoft-json"}, {"version": "1.3.2", "name": "com.unity.mathematics"}], "location": "https://packages.unity.com"}, {"version": "2.1.0", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.settings-manager@41738c275190", "name": "com.unity.settings-manager", "dependencies": [], "location": "https://packages.unity.com"}, {"version": "1.0.0-pre.12", "source": "Registry", "resolvedPath": "F:\\Match2D\\Library\\PackageCache\\com.unity.ai.toolkit@97783faabe3b", "name": "com.unity.ai.toolkit", "dependencies": [{"version": "3.2.1", "name": "com.unity.nuget.newtonsoft-json"}], "location": "https://packages.unity.com"}, {"version": "1.0", "source": "AssetStore", "resolvedPath": "", "name": "2D Casual UI HD", "dependencies": [], "location": ""}, {"version": "1.0", "source": "AssetStore", "resolvedPath": "", "name": "2D Casual Game UI HD", "dependencies": [], "location": ""}, {"version": "1.0", "source": "AssetStore", "resolvedPath": "", "name": "Hyper casual mobile GUI", "dependencies": [], "location": ""}, {"version": "1.0", "source": "AssetStore", "resolvedPath": "", "name": "Puzzle Blocks Icon Pack", "dependencies": [], "location": ""}, {"version": "1.6", "source": "AssetStore", "resolvedPath": "", "name": "Gem Hunter Match - 2D Sample Project", "dependencies": [], "location": ""}, {"version": "1.0.0", "source": "AssetStore", "resolvedPath": "", "name": "Fantasy Music Package", "dependencies": [], "location": ""}, {"version": "1.6", "source": "AssetStore", "resolvedPath": "", "name": "Magic Effects FREE", "dependencies": [], "location": ""}, {"version": "R 1.5.0", "source": "AssetStore", "resolvedPath": "", "name": "Cartoon FX Remaster Free", "dependencies": [], "location": ""}, {"version": "1.0.1", "source": "AssetStore", "resolvedPath": "", "name": "Hyper Casual FX", "dependencies": [], "location": ""}, {"version": "1.3.1", "source": "AssetStore", "resolvedPath": "", "name": "PrimeTween · High-Performance Animations and Sequences", "dependencies": [], "location": ""}, {"version": "1.0", "source": "AssetStore", "resolvedPath": "", "name": "Hungry Bat - Match 3 UI", "dependencies": [], "location": ""}, {"version": "1.0", "source": "AssetStore", "resolvedPath": "", "name": "Sweet Land GUI", "dependencies": [], "location": ""}], "PatchPackage": false, "ArchitectureExtensions": "None", "ApplicationEntry": "GameActivity", "BuildFingerPrintContents": "6000.2.0b9;IL2CPP;Release;StripEngineCode:1;OptimizedFramePacing:0;AppEntry:2;LTO:0", "UserSymbols": [], "ConfigurationManagerAssemblies": ["F:\\Match2D\\Library\\ScriptAssemblies\\Unity.AppUI.Editor.dll"], "PostGenerateGradleCallbackUsed": false, "TrackedFeatures": ["UnityEngine.Android.AndroidGame::SetGameState", "UnityEngine.Android.AndroidGame::get_GameMode", "UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn", "UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"], "ApplicationName": "Puffland Adventure", "StaticSplashScreenBackgroundColor": "231F20", "APIRequiringInternetPermission": ["UnityEngine.Networking", "System.Net.Sockets", "System.Net.WebRequest", "UnityEngine.Ping", "UnityEngine.Networking.UnityWebRequest"]}, "AndroidPlayerBuildProgram.Data.AndroidManifestConfiguration": {"TargetSDKVersion": 36, "LauncherManifestTemplatePath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\LauncherManifest.xml", "LauncherManifestTemplateUsed": false, "LibraryManifestTemplatePath": "Assets/Plugins/Android\\AndroidManifest.xml", "LibraryManifestCustomTemplateUsed": true, "LauncherManifestPath": "launcher\\src\\main\\AndroidManifest.xml", "LibraryManifestPath": "unityLibrary\\src\\main\\AndroidManifest.xml", "TVCompatibility": false, "BannerEnabled": true, "IsGame": true, "PreferredInstallLocation": "PreferExternal", "TextureSupport": "Generic", "GamepadSupportLevel": "SupportsDPad", "SupportedAspectRatioMode": 1, "MaxAspectRatio": 2.4, "MinAspectRatio": 1, "ForceInternetPermission": true, "UseLowAccuracyLocation": false, "ForceSDCardPermission": false, "PreserveFramebufferAlpha": false, "DefaultInterfaceOrientation": "AutoRotation", "AllowedAutorotateToPortrait": true, "AllowedAutorotateToPortraitUpsideDown": true, "AllowedAutorotateToLandscapeLeft": false, "AllowedAutorotateToLandscapeRight": false, "SplashScreenScale": "Center", "RenderOutsideSafeArea": true, "GraphicsDevices": ["OpenGLES3"], "OpenGLRequireES31": false, "OpenGLRequireES31AEP": false, "OpenGLRequireES32": false, "StartInFullscreen": true, "DefaultWindowWidth": 1920, "DefaultWindowHeight": 1080, "MinimumWindowWidth": 400, "MinimumWindowHeight": 300, "ResizeableActivity": true, "FullScreenMode": "FullScreenWindow", "AutoRotationBehavior": "User", "StripEngineCode": true, "ApplicationEntry": "GameActivity", "JavaFileNames": ["UnityPlayerGameActivity.java", "UnityLevelPlayBannerListener.java", "UnityLevelPlayRewardedVideoListener.java", "RewardedAd.java", "IUnityBannerAdListener.java", "UnitySegmentListener.java", "InterstitialAd.java", "UnityImpressionDataListener.java", "AndroidBridgeConstants.java", "BannerAd.java", "IUnityRewardedAdListener.java", "HapticFeedback.java", "LevelPlayRewardedVideoWrapper.java", "AndroidBridge.java", "IUnityLevelPlayInitListener.java", "UnityInitializationListener.java", "UnityLevelPlayInterstitialListener.java", "BannerUtils.java", "IUnityInterstitialAdListener.java", "LevelPlayUtils.java", "LevelPlayBridge.java", "AndroidBridgeUtilities.java", "UnityLevelPlayRewardedVideoManualListener.java", "LevelPlayInterstitialWrapper.java"], "EnableOnBackInvokedCallback": false}, "AndroidPlayerBuildProgram.Data.AndroidSharedLibraryConfiguration": {"ClangPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe", "ABITools": [{"Architecture": "ARM64", "ObjCopyPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-objcopy.exe", "StripPath": "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe"}]}}