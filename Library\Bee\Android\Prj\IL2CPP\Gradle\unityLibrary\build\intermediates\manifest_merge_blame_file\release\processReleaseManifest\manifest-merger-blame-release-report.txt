1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.unity3d.player" >
5
6    <uses-sdk android:minSdkVersion="23" />
7
8    <uses-permission android:name="android.permission.VIBRATE" />
8-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:3-64
8-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:20-61
9    <uses-permission android:name="android.permission.INTERNET" />
9-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:3-65
9-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:20-62
10
11    <uses-feature android:glEsVersion="0x00030000" />
11-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:3-52
11-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:17-49
12    <uses-feature
12-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:3-88
13        android:name="android.hardware.touchscreen"
13-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:17-60
14        android:required="false" />
14-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:61-85
15    <uses-feature
15-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:3-99
16        android:name="android.hardware.touchscreen.multitouch"
16-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:17-71
17        android:required="false" />
17-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:72-96
18    <uses-feature
18-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:3-108
19        android:name="android.hardware.touchscreen.multitouch.distinct"
19-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:17-80
20        android:required="false" />
20-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:81-105
21
22    <application
22-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:3-31:17
23        android:allowBackup="false"
23-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:16-43
24        android:enableOnBackInvokedCallback="false"
24-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:44-87
25        android:extractNativeLibs="true"
25-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:88-120
26        android:usesCleartextTraffic="false" >
26-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:121-157
27        <meta-data
27-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:5-130
28            android:name="com.google.android.gms.ads.APPLICATION_ID"
28-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:16-72
29            android:value="ca-app-pub-3940256099942544~3347511713" />
29-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:73-127
30        <meta-data
30-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:5-69
31            android:name="unity.splash-mode"
31-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:16-48
32            android:value="0" />
32-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:49-66
33        <meta-data
33-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:5-74
34            android:name="unity.splash-enable"
34-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:16-50
35            android:value="True" />
35-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:51-71
36        <meta-data
36-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:5-78
37            android:name="unity.launch-fullscreen"
37-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:16-54
38            android:value="True" />
38-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:55-75
39        <meta-data
39-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:5-84
40            android:name="unity.render-outside-safearea"
40-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:16-60
41            android:value="True" />
41-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:61-81
42        <meta-data
42-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:5-81
43            android:name="notch.config"
43-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:16-43
44            android:value="portrait|landscape" />
44-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:44-78
45        <meta-data
45-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:5-84
46            android:name="unity.auto-report-fully-drawn"
46-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:16-60
47            android:value="true" />
47-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:61-81
48        <meta-data
48-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:5-80
49            android:name="unity.auto-set-game-state"
49-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:16-56
50            android:value="true" />
50-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:57-77
51        <meta-data
51-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:5-78
52            android:name="unity.strip-engine-code"
52-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:16-54
53            android:value="true" />
53-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:55-75
54
55        <activity
55-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:5-30:16
56            android:name="com.unity3d.player.appui.AppUIGameActivity"
56-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:289-346
57            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
57-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:15-196
58            android:exported="true"
58-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:197-220
59            android:hardwareAccelerated="false"
59-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:221-256
60            android:launchMode="singleTask"
60-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:257-288
61            android:resizeableActivity="true"
61-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:347-380
62            android:screenOrientation="userPortrait"
62-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:381-421
63            android:theme="@style/BaseUnityGameActivityTheme" >
63-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:422-471
64            <intent-filter>
64-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:7-23:23
65                <category android:name="android.intent.category.LAUNCHER" />
65-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:9-69
65-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:19-66
66
67                <action android:name="android.intent.action.MAIN" />
67-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:9-61
67-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:17-58
68            </intent-filter>
69
70            <meta-data
70-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:7-82
71                android:name="unityplayer.UnityActivity"
71-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:18-58
72                android:value="true" />
72-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:59-79
73            <meta-data
73-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:7-77
74                android:name="android.app.lib_name"
74-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:18-53
75                android:value="game" />
75-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:54-74
76            <meta-data
76-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:7-130
77                android:name="WindowManagerPreference:FreeformWindowSize"
77-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:18-75
78                android:value="@string/FreeformWindowSize_maximize" />
78-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:76-127
79            <meta-data
79-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:7-144
80                android:name="WindowManagerPreference:FreeformWindowOrientation"
80-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:18-82
81                android:value="@string/FreeformWindowOrientation_portrait" />
81-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:83-141
82            <meta-data
82-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:7-70
83                android:name="notch_support"
83-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:18-46
84                android:value="true" />
84-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:47-67
85
86            <layout
86-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:7-68
87                android:minHeight="300px"
87-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:15-40
88                android:minWidth="400px" />
88-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:41-65
89        </activity>
90    </application>
91
92</manifest>
