{ "pid": 27808, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 27808, "tid": 1, "ts": 1753393149278682, "dur": 9277, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 27808, "tid": 1, "ts": 1753393149287965, "dur": 285314, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 27808, "tid": 1, "ts": 1753393149573289, "dur": 11559, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 27808, "tid": 7937, "ts": 1753393150953350, "dur": 1752, "ph": "X", "name": "", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149276678, "dur": 6352, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149283034, "dur": 1656590, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149284123, "dur": 2117, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149286248, "dur": 540, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149286792, "dur": 7934, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149294734, "dur": 189, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149294927, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149294984, "dur": 738, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149295756, "dur": 3231, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149298998, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149299029, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149299105, "dur": 727, "ph": "X", "name": "ProcessMessages 741", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149299836, "dur": 56, "ph": "X", "name": "ReadAsync 741", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149299895, "dur": 232, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149300130, "dur": 878, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149301012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149301014, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149301051, "dur": 565, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393149301620, "dur": 1621674, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393150923306, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393150923311, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393150923342, "dur": 2039, "ph": "X", "name": "ProcessMessages 264", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393150925386, "dur": 80, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393150925470, "dur": 246, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 27808, "tid": 12884901888, "ts": 1753393150925719, "dur": 13161, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 27808, "tid": 7937, "ts": 1753393150955106, "dur": 86, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 27808, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 27808, "tid": 8589934592, "ts": 1753393149272081, "dur": 312805, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 27808, "tid": 8589934592, "ts": 1753393149584888, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 27808, "tid": 8589934592, "ts": 1753393149584899, "dur": 1351, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 27808, "tid": 7937, "ts": 1753393150955194, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 27808, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 27808, "tid": 4294967296, "ts": 1753393149252956, "dur": 1687738, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 27808, "tid": 4294967296, "ts": 1753393149256559, "dur": 10045, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 27808, "tid": 4294967296, "ts": 1753393150940768, "dur": 4843, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 27808, "tid": 4294967296, "ts": 1753393150943871, "dur": 41, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 27808, "tid": 4294967296, "ts": 1753393150945718, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 27808, "tid": 7937, "ts": 1753393150955199, "dur": 149, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753393149280004, "dur":17326, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393149297342, "dur":417, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393149297789, "dur":315, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393149298135, "dur":84, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393149298220, "dur":1624777, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393150922998, "dur":92, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393150923174, "dur":50, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393150923255, "dur":8279, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753393149298357, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753393149298642, "dur":934, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\OwnMatch3.dll" }}
,{ "pid":12345, "tid":1, "ts":1753393149298422, "dur":1304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1753393149300591, "dur":1621914, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1753393149298384, "dur":224, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753393149298641, "dur":287812, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753393149586454, "dur":1336549, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753393149298374, "dur":286, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753393149298661, "dur":1624335, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753393149298415, "dur":219, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753393149298634, "dur":286792, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753393149586300, "dur":138, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1753393149585427, "dur":1018, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753393149586446, "dur":1336568, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753393149298554, "dur":171, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753393149298725, "dur":1624240, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753393149298687, "dur":1624315, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753393149298712, "dur":1624289, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753393149298732, "dur":1624231, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753393150937497, "dur":268, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 27808, "tid": 7937, "ts": 1753393150956756, "dur": 2000, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 27808, "tid": 7937, "ts": 1753393150958812, "dur": 722, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 27808, "tid": 7937, "ts": 1753393150952015, "dur": 8292, "ph": "X", "name": "Write chrome-trace events", "args": {} },
