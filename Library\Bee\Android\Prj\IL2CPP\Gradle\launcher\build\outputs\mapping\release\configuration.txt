# The proguard configuration file for the following section is F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.games.internal.v2.appshortcuts.PlayGamesAppShortcutsActivity { <init>(); }
-keep class com.google.android.gms.games.internal.v2.resolution.GamesResolutionActivity { <init>(); }
-keep class com.google.android.gms.games.provider.PlayGamesInitProvider { <init>(); }
-keep class com.google.android.gms.nearby.exposurenotification.WakeUpService { <init>(); }
-keep class com.google.android.play.core.assetpacks.AssetPackExtractionService { <init>(); }
-keep class com.google.android.play.core.assetpacks.ExtractionForegroundService { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.games.bridge.GenericResolutionActivity { <init>(); }
-keep class com.google.games.bridge.NativeBridgeActivity { <init>(); }
-keep class com.ironsource.lifecycle.IronsourceLifecycleProvider { <init>(); }
-keep class com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider { <init>(); }
-keep class com.ironsource.mediationsdk.testSuite.TestSuiteActivity { <init>(); }
-keep class com.ironsource.sdk.controller.ControllerActivity { <init>(); }
-keep class com.ironsource.sdk.controller.InterstitialActivity { <init>(); }
-keep class com.ironsource.sdk.controller.OpenUrlActivity { <init>(); }
-keep class com.unity3d.ads.adplayer.FullScreenWebViewDisplay { <init>(); }
-keep class com.unity3d.player.appui.AppUIGameActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitSoftwareActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitTransparentActivity { <init>(); }
-keep class com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\intermediates\default_proguard_files\global\proguard-android.txt-8.7.2
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize steps (and performs some
# of these optimizations on its own).
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.
-dontoptimize

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\intermediates\default_proguard_files\global\proguard-android.txt-8.7.2
# The proguard configuration file for the following section is F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep class bitter.jnibridge.* { *; }
-keep class com.unity3d.player.* { *; }
-keep interface com.unity3d.player.IUnityPlayerLifecycleEvents { *; }
-keep class org.fmod.* { *; }
-keep class androidx.core.graphics.Insets** { *; }
-keep class androidx.core.view.WindowInsetsCompat** { *; }
-keep class com.google.android.games.paddleboat.** { *; }
-keep class com.google.androidgamesdk.gametextinput.** { *; }
-keepclassmembers class com.google.androidgamesdk.GameActivity {
  void setWindowFlags(int, int);
  public androidx.core.graphics.Insets getWindowInsets(int);
  public androidx.core.graphics.Insets getWaterfallInsets();
  public void setImeEditorInfo(android.view.inputmethod.EditorInfo);
  public void setImeEditorInfoFields(int, int, int);
}
-ignorewarnings

# End of content from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib1\proguard.txt
-keep class com.google.games.** { *; }
-keep interface com.google.games.** { *; }
-keep class com.google.unity.** { *; }
 
 -keep class com.unity3d.plugin.lvl.ServiceBinder { *; }
 -keep class com.google.android.gms.games.multiplayer.** { *; }
 -keep class com.google.android.gms.games.leaderboard.** { *; }
 -keep class com.google.android.gms.games.snapshot.** { *; }
 -keep class com.google.android.gms.games.achievement.** { *; }
 -keep class com.google.android.gms.games.event.** { *; }
 -keep class com.google.android.gms.games.stats.** { *; }
 -keep class com.google.android.gms.games.video.** { *; }
 -keep class com.google.android.gms.games.* { *; }
 -keep class com.google.android.gms.common.api.ResultCallback { *; }
 -keep class com.google.android.gms.signin.** { *; }
 -keep class com.google.android.gms.dynamic.** { *; }
 -keep class com.google.android.gms.dynamite.** { *; }
 -keep class com.google.android.play.core.review.** { *; }
 -keep class com.google.android.play.core.appupdate.** { *; }
 -keep class com.google.android.gms.tasks.** { *; }
 -keep class com.google.android.gms.security.** { *; }
 -keep class com.google.android.gms.base.** { *; }
 -keep class com.google.android.gms.actions.** { *; }
 -keep class com.google.games.bridge.** { *; }
 
 -keep class com.google.android.gms.common.ConnectionResult { *; }
 -keep class com.google.android.gms.common.GooglePlayServicesUtil { *; }
 -keep class com.google.android.gms.common.api.** { *; }
 -keep class com.google.android.gms.common.data.DataBufferUtils { *; }
 -keep class com.google.android.gms.games.quest.** { *; }
 -keep class com.google.android.gms.nearby.** { *; }

 -keep class com.google.android.play.core.appupdate.AppUpdateInfo {
    public java.lang.String packageName();
    public int availableVersionCode();
    public int updateAvailability();
    public int installStatus();
    public boolean isUpdateTypeAllowed(com.google.android.play.core.appupdate.AppUpdateOptions);
    public boolean isUpdateTypeAllowed(int);
    public int updatePriority();
    public long bytesDownloaded();
    public long totalBytesToDownload();
    public java.lang.Integer clientVersionStalenessDays();
}

-keep class com.google.android.play.core.appupdate.AppUpdateManager {
    public abstract void registerListener(com.google.android.play.core.install.InstallStateUpdatedListener);
    public abstract void unregisterListener(com.google.android.play.core.install.InstallStateUpdatedListener);

    public abstract com.google.android.play.core.tasks.Task completeUpdate();
    public abstract com.google.android.play.core.tasks.Task getAppUpdateInfo();
    public com.google.android.play.core.tasks.Task startUpdateFlow(com.google.android.play.core.appupdate.AppUpdateInfo, android.app.Activity, com.google.android.play.core.appupdate.AppUpdateOptions);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, android.app.Activity, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, com.google.android.play.core.common.IntentSenderForResultStarter, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, android.app.Activity, com.google.android.play.core.appupdate.AppUpdateOptions, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, com.google.android.play.core.common.IntentSenderForResultStarter, com.google.android.play.core.appupdate.AppUpdateOptions, int);
}

-keep class com.google.android.play.core.appupdate.AppUpdateManagerFactory {
    <init>();

    public static com.google.android.play.core.appupdate.AppUpdateManager create(android.content.Context);
}

-keep class com.google.android.play.core.appupdate.AppUpdateOptions {
    public abstract boolean allowAssetPackDeletion();
    public abstract int appUpdateType();
    public static com.google.android.play.core.appupdate.AppUpdateOptions$Builder newBuilder(int);
    public static com.google.android.play.core.appupdate.AppUpdateOptions defaultOptions(int);
}

-keep class com.google.android.play.core.appupdate.AppUpdateOptions$Builder {
    public abstract com.google.android.play.core.appupdate.AppUpdateOptions$Builder setAllowAssetPackDeletion(boolean);
    public abstract com.google.android.play.core.appupdate.AppUpdateOptions$Builder setAppUpdateType(int);
    public abstract com.google.android.play.core.appupdate.AppUpdateOptions build();
}

-keep class com.google.android.play.core.appupdate.testing.FakeAppUpdateManager {
    public <init>(android.content.Context);

    public void registerListener(com.google.android.play.core.install.InstallStateUpdatedListener);
    public void unregisterListener(com.google.android.play.core.install.InstallStateUpdatedListener);

    public com.google.android.play.core.tasks.Task completeUpdate();
    public com.google.android.play.core.tasks.Task getAppUpdateInfo();
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, android.app.Activity, int);
    public boolean startUpdateFlowForResult(com.google.android.play.core.appupdate.AppUpdateInfo, int, com.google.android.play.core.common.IntentSenderForResultStarter, int);

    public void setUpdateAvailable(int);
    public void setUpdateAvailable(int, int);
    public void setUpdateNotAvailable();
    public void setInstallErrorCode(int);

    public void userAcceptsUpdate();
    public void userRejectsUpdate();

    public void downloadStarts();
    public void downloadCompletes();
    public void userCancelsDownload();
    public void downloadFails();

    public void installCompletes();
    public void installFails();

    public boolean isConfirmationDialogVisible();
    public boolean isImmediateFlowVisible();
    public boolean isInstallSplashScreenVisible();
    public java.lang.Integer getTypeForUpdateInProgress();

    public void setClientVersionStalenessDays(java.lang.Integer);
    public void setTotalBytesToDownload(long);
    public void setBytesDownloaded(long);
    public void setUpdatePriority(int);
}

-keep class com.google.android.play.core.install.InstallException {
    public int getErrorCode();
}

-keep class com.google.android.play.core.install.InstallState {
    public int installErrorCode();
    public int installStatus();
    public java.lang.String packageName();
    public long bytesDownloaded();
    public long totalBytesToDownload();
}

-keep class com.google.android.play.core.install.InstallStateUpdatedListener {
   <init>();

   public void onStateUpdate(com.google.android.play.core.install.InstallState);
}

-keep class com.google.android.play.core.install.NativeInstallStateUpdateListener {
    <init>();

    public void onStateUpdate(com.google.android.play.core.install.InstallState);
}

-keep class com.google.android.play.core.install.model.ActivityResult {
    public static int RESULT_IN_APP_UPDATE_FAILED;
}

-keep class com.google.android.play.core.install.model.AppUpdateType {
    public static int FLEXIBLE;
    public static int IMMEDIATE;
}

-keep class com.google.android.play.core.install.model.InstallErrorCode {
    public static int NO_ERROR;
    public static int NO_ERROR_PARTIALLY_ALLOWED;
    public static int ERROR_API_NOT_AVAILABLE;
    public static int ERROR_APP_NOT_OWNED;
    public static int ERROR_DOWNLOAD_NOT_PRESENT;
    public static int ERROR_INSTALL_NOT_ALLOWED;
    public static int ERROR_INSTALL_UNAVAILABLE;
    public static int ERROR_INTERNAL_ERROR;
    public static int ERROR_INVALID_REQUEST;
    public static int ERROR_PLAY_STORE_NOT_FOUND;
    public static int ERROR_UNKNOWN;
}

-keep class com.google.android.play.core.install.model.InstallStatus {
    public static int CANCELED;
    public static int DOWNLOADED;
    public static int DOWNLOADING;
    public static int FAILED;
    public static int INSTALLED;
    public static int INSTALLING;
    public static int PENDING;
    public static int REQUIRES_UI_INTENT;
    public static int UNKNOWN;
}

-keep class com.google.android.play.core.install.model.UpdateAvailability {
    public static int UNKNOWN;
    public static int UPDATE_AVAILABLE;
    public static int UPDATE_NOT_AVAILABLE;
    public static int DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS;
}

 -keepclassmembers class fqcn.of.javascript.interface.for.webview { public *;}
 -target 1.6
 -optimizationpasses 2
 -dontusemixedcaseclassnames
 -dontskipnonpubliclibraryclasses
 -dontpreverify
 -keepattributes InnerClasses,EnclosingMethod

 -optimizations !code/simplification/arithmetic

 -keep class * { public <methods>; !private *; }

  -dontobfuscate
# End of content from F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\proguard.txt
# Keep filenames and line numbers for stack traces
-keepattributes SourceFile,LineNumberTable

# Keep JavascriptInterface for WebView bridge
-keepattributes JavascriptInterface

# Sometimes keepattributes is not enough to keep annotations
-keep class android.webkit.JavascriptInterface {
   *;
}

# Keep all classes in Unity Ads package
-keep class com.unity3d.ads.** {
   *;
}

# Keep all classes in Unity Services package
-keep class com.unity3d.services.** {
   *;
}

-keep class com.google.android.gms.ads.initialization.** {
        *;
}

-keep class com.google.android.gms.ads.MobileAds {
        *;
}

-keep public class gatewayprotocol.v1.** {
    *;
}

-keep public class com.google.protobuf.** {
    *;
 }

 -keep public class com.android.billingclient.api.* {public *;}

-dontwarn com.google.ads.mediation.admob.*
-dontwarn com.google.android.gms.ads.**

# Presence of Android Privacy Sandbox services is handled with runtime checks
# Compile time ProGuard checks can lead to problems with developers (ABT-3131)
-dontwarn android.adservices.**

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\proguard.txt
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}


#unity
-keep class com.ironsource.unity.androidbridge.** { *;}

-keep class com.google.android.gms.ads.** {public *;}

-keep class com.google.android.gms.appset.** { *; }
-keep class com.google.android.gms.tasks.** { *; }

#adapters
-keep class com.ironsource.adapters.** { *; }

#sdk
-dontwarn com.ironsource.**
-keep class com.ironsource.** { *; }
-dontwarn com.unity3d.ironsourceads.**
-keep class com.unity3d.ironsourceads.** { *;}
-dontwarn com.unity3d.mediation.**
-keep class com.unity3d.mediation.** { *;}

#omid
-dontwarn com.iab.omid.**
-keep class com.iab.omid.** {*;}

#javascript
-keepattributes JavascriptInterface
-keepclassmembers class * { @android.webkit.JavascriptInterface <methods>; }
# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\b8cedf25c0a31d0d98ed30b050a78fe7\transformed\jetified-unityads-adapter-4.3.57\proguard.txt
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Desktop/adt-bundle-mac-x86_64-20140702/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
# Keep filenames and line numbers for stack traces
-keepattributes SourceFile,LineNumberTable

# Keep JavascriptInterface for WebView bridge
-keepattributes JavascriptInterface

# Sometimes keepattributes is not enough to keep annotations
-keep class android.webkit.JavascriptInterface {
   *;
}

# Keep all classes in Unity Ads package
-keep class com.unity3d.ads.** {
   *;
}

# Keep all classes in Unity Services package
-keep class com.unity3d.services.** {
   *;
}

-dontwarn com.google.ar.core.**
# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\b8cedf25c0a31d0d98ed30b050a78fe7\transformed\jetified-unityads-adapter-4.3.57\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\5fb357593bf2e41df09357350faade82\transformed\appcompat-1.6.1\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\5fb357593bf2e41df09357350faade82\transformed\appcompat-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\proguard.txt
# Keep all of Cronet API as it's used by the Cronet module.
-keep public class org.chromium.net.* {
    !private *;
    *;
}


# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\30fbf7abc4070fa1e2ce7bca99307007\transformed\jetified-play-services-cronet-18.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.drive.zzkk {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\2abc264b9df41e4a10b5ed3f548577d9\transformed\jetified-play-services-drive-17.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\07b9ad58ba10f280dfbacdca436e4963\transformed\jetified-play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\022132af6bac98e8948adfeadca2b88c\transformed\fragment-1.3.6\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\022132af6bac98e8948adfeadca2b88c\transformed\fragment-1.3.6\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\2e921cd16d63f4e0d0a788ce4333e813\transformed\jetified-savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\2e921cd16d63f4e0d0a788ce4333e813\transformed\jetified-savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker (also marked with @Keep)
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\0acb1fec1e7ba4af001e41f13611abe5\transformed\lifecycle-viewmodel-2.6.1\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\0acb1fec1e7ba4af001e41f13611abe5\transformed\lifecycle-viewmodel-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\854426de3cc1265ebe6fde1d66be3ffb\transformed\lifecycle-runtime-2.6.1\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep !interface * implements androidx.lifecycle.LifecycleObserver {
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\854426de3cc1265ebe6fde1d66be3ffb\transformed\lifecycle-runtime-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\d7fe1bbac8c4910b21f08d7fc9f6ca4c\transformed\jetified-lifecycle-process-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\f208bdbdbf7b72a4088cabd7ffa3dac0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\f208bdbdbf7b72a4088cabd7ffa3dac0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\b7a5ce217afa85a406cc43d98dc74508\transformed\webkit-1.6.1\proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\b7a5ce217afa85a406cc43d98dc74508\transformed\webkit-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\30fba741f8e98cb753260dbf350d5148\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\30fba741f8e98cb753260dbf350d5148\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\5652c66f07446d89a89a7ab2b3eed394\transformed\jetified-games-activity-3.0.5\proguard.txt
-keep class androidx.core.graphics.Insets** { *; }
-keep class androidx.core.view.WindowInsetsCompat** { *; }
-keep class com.google.android.games.paddleboat.** { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\5652c66f07446d89a89a7ab2b3eed394\transformed\jetified-games-activity-3.0.5\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\5914cd0d1f7ce5b2d77c09c643cca61b\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\e25ffe5662076be5a8e885fcdb8cc42e\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\e25ffe5662076be5a8e885fcdb8cc42e\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\31880a0c24b37ea14a793fcbc699fee5\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\31880a0c24b37ea14a793fcbc699fee5\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\2cc3686114099940fbb59f1aba15e98b\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\2cc3686114099940fbb59f1aba15e98b\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\proguard.txt



-dontwarn com.ironsource.adqualitysdk.sdk.**




-keep public class com.ironsource.adqualitysdk.sdk.* {
    public <fields>;    public <methods>;
}

-keep public class com.ironsource.adqualitysdk.sdk.unity.* {
    public <fields>;    public <methods>;
}

# For AmazonAps integration
-keep class com.amazon.device.ads.DtbThreadService {
    static <fields>;    static <methods>;
}

-keep public interface  com.amazon.device.ads** {
    <fields>;    <methods>;
}

# For AppLovin integration
-keepclassmembers class com.applovin.sdk.AppLovinSdk {
    static <fields>;    static <methods>;
}

-keep public interface  com.applovin.sdk** {
    <fields>;    <methods>;
}

-keep public interface  com.applovin.adview** {
    <fields>;    <methods>;
}

-keep public interface  com.applovin.mediation** {
    <fields>;    <methods>;
}

-keep public interface  com.applovin.communicator** {
    <fields>;    <methods>;
}

# For Bytedance integration
-keep public interface  com.bytedance.sdk.openadsdk** {
    <fields>;    <methods>;
}

# For Facebook integration
-keepclassmembers class com.facebook.ads.internal.AdSdkVersion {
    static <fields>;    static <methods>;
}

-keepclassmembers class com.facebook.ads.internal.settings.AdSdkVersion {
    static <fields>;    static <methods>;
}

-keepclassmembers class com.facebook.ads.BuildConfig {
    static <fields>;    static <methods>;
}

-keep public interface  com.facebook.ads** {
    <fields>;    <methods>;
}

# For Fairbid
-keep public interface  com.fyber.fairbid.ads.interstitial** {
    <fields>;    <methods>;
}

-keep public interface  com.fyber.fairbid.ads.rewarded** {
    <fields>;    <methods>;
}

-keep class com.fyber.offerwall.*

# For Fivead
-keep public interface  com.five_corp.ad** {
    <fields>;    <methods>;
}

# For Fyber(Inneractive) integration
-keep public interface  com.fyber.inneractive.sdk.external** {
    <fields>;    <methods>;
}

-keep public interface  com.fyber.inneractive.sdk.activities** {
    <fields>;    <methods>;
}

-keep public interface  com.fyber.inneractive.sdk.ui** {
    <fields>;    <methods>;
}

# For HyprMX integration
-keepclassmembers class com.hyprmx.android.sdk.utility.HyprMXProperties {
    static <fields>;    static <methods>;
}

-keepclassmembers class com.hyprmx.android.BuildConfig {
    static <fields>;    static <methods>;
}

-keep public interface  com.hyprmx.android.sdk.activity** {
    <fields>;    <methods>;
}

-keep public interface  com.hyprmx.android.sdk.graphics** {
    <fields>;    <methods>;
}

# For Inmobi integration
-keep class com.inmobi.*

-keep public interface  com.inmobi.ads.listeners** {
    <fields>;    <methods>;
}

-keep public interface  com.inmobi.ads.InMobiInterstitial** {
    <fields>;    <methods>;
}

-keep public interface  com.inmobi.ads.InMobiBanner** {
    <fields>;    <methods>;
}

# For ironSource integration
-keep public interface  com.ironsource.mediationsdk.sdk** {
    <fields>;    <methods>;
}

-keep public interface  com.ironsource.mediationsdk.impressionData.ImpressionDataListener {
    <fields>;    <methods>;
}

# For Maio integration
-keep public interface  jp.maio.sdk.android.MaioAdsListenerInterface {
    <fields>;    <methods>;
}

# For Mintergral integration
-keep public interface  com.mbridge.msdk.out** {
    <fields>;    <methods>;
}

-keep public interface  com.mbridge.msdk.videocommon.listener** {
    <fields>;    <methods>;
}

-keep public interface  com.mbridge.msdk.interstitialvideo.out** {
    <fields>;    <methods>;
}

-keep public interface  com.mintegral.msdk.out** {
    <fields>;    <methods>;
}

-keep public interface  com.mintegral.msdk.videocommon.listener** {
    <fields>;    <methods>;
}

-keep public interface  com.mintegral.msdk.interstitialvideo.out** {
    <fields>;    <methods>;
}

# For Moloco integration
-keep public interface  com.moloco.sdk.publisher.** {
    <fields>;    <methods>;
}

# For MyTarget integration
-keep class com.my.target.** {
    <fields>;    <methods>;
}

# For Ogury integration
-keep public interface  io.presage.interstitial** {
    <fields>;    <methods>;
}

-keep public interface  io.presage.interstitial.PresageInterstitialCallback {
    <fields>;    <methods>;
}

# For Pubnative integration
-keep public interface  net.pubnative.lite.sdk.interstitial.HyBidInterstitialAd** {
    <fields>;    <methods>;
}

-keep public interface  net.pubnative.lite.sdk.rewarded.HyBidRewardedAd** {
    <fields>;    <methods>;
}

-keep public interface  net.pubnative.lite.sdk.views.HyBidAdView** {
    <fields>;    <methods>;
}

# For Smaato integration
-keep public interface  com.smaato.sdk.interstitial** {
    <fields>;    <methods>;
}

-keep public interface  com.smaato.sdk.video.vast** {
    <fields>;    <methods>;
}

-keep public interface  com.smaato.sdk.banner.widget** {
    <fields>;    <methods>;
}

-keep public interface  com.smaato.sdk.core.util** {
    <fields>;    <methods>;
}

# For SuperAwesome integration
-keep public interface  tv.superawesome.sdk.publisher.SAInterface {
    <fields>;    <methods>;
}

-keep public interface  tv.superawesome.sdk.publisher.videoPlayer** {
    <fields>;    <methods>;
}

# For Tapjoy integration
-keep public interface  com.tapjoy.** {
    <fields>;    <methods>;
}

# For Tencent integration
-keep public interface  com.qq.e.ads.interstitial2** {
    <fields>;    <methods>;
}

-keep public interface  com.qq.e.ads.interstitial3** {
    <fields>;    <methods>;
}

-keep public interface  com.qq.e.ads.rewardvideo** {
    <fields>;    <methods>;
}

-keep public interface  com.qq.e.ads.rewardvideo2** {
    <fields>;    <methods>;
}

-keep public interface  com.qq.e.ads.banner2** {
    <fields>;    <methods>;
}

-keep public interface  com.qq.e.comm.adevent** {
    <fields>;    <methods>;
}

-keep public interface  com.verizon.ads.interstitialplacement** {
    <fields>;    <methods>;
}

-keep public interface  com.verizon.ads.inlineplacement** {
    <fields>;    <methods>;
}

-keep public interface  com.verizon.ads.vastcontroller** {
    <fields>;    <methods>;
}

-keep public interface  com.verizon.ads.webcontroller** {
    <fields>;    <methods>;
}

# For Vungle integration
-keep public interface  com.vungle.warren.PlayAdCallback {
    <fields>;    <methods>;
}

-keep public interface  com.vungle.warren.ui.contract** {
    <fields>;    <methods>;
}

-keep public interface  com.vungle.warren.ui.view** {
    <fields>;    <methods>;
}

# For Yandex integration
-keep public interface  com.yandex.mobile.ads.banner** {
    <fields>;    <methods>;
}

-keep public interface  com.yandex.mobile.ads.interstitial** {
    <fields>;    <methods>;
}

-keep public interface  com.yandex.mobile.ads.rewarded** {
    <fields>;    <methods>;
}

-keep public interface  com.yandex.mobile.ads.video.playback** {
    <fields>;    <methods>;
}

# For AndroidX
-keep class androidx.localbroadcastmanager.content.LocalBroadcastManager {
    <fields>;    <methods>;
}

-keep class androidx.recyclerview.widget.RecyclerView {
    <fields>;    <methods>;
}

-keep class androidx.recyclerview.widget.RecyclerView$OnScrollListener {
    <fields>;    <methods>;
}

# For Android
-keep class * extends android.app.Activity

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\b9f85c476eaf85301d79092f7795a901\transformed\jetified-adquality-sdk-7.25.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\37cbb73f3bb006c5cbb9064d51f4a190\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.9\transforms\b5ab7fef75bf6d08420d298c65542257\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.9\transforms\b5ab7fef75bf6d08420d298c65542257\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>