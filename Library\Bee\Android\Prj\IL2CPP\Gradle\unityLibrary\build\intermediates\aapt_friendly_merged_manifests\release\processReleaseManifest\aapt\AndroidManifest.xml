<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.unity3d.player" >

    <uses-sdk android:minSdkVersion="23" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature android:glEsVersion="0x00030000" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch.distinct"
        android:required="false" />

    <application
        android:allowBackup="false"
        android:enableOnBackInvokedCallback="false"
        android:extractNativeLibs="true"
        android:usesCleartextTraffic="false" >
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~3347511713" />
        <meta-data
            android:name="unity.splash-mode"
            android:value="0" />
        <meta-data
            android:name="unity.splash-enable"
            android:value="True" />
        <meta-data
            android:name="unity.launch-fullscreen"
            android:value="True" />
        <meta-data
            android:name="unity.render-outside-safearea"
            android:value="True" />
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <meta-data
            android:name="unity.auto-report-fully-drawn"
            android:value="true" />
        <meta-data
            android:name="unity.auto-set-game-state"
            android:value="true" />
        <meta-data
            android:name="unity.strip-engine-code"
            android:value="true" />

        <activity
            android:name="com.unity3d.player.appui.AppUIGameActivity"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
            android:exported="true"
            android:hardwareAccelerated="false"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:screenOrientation="userPortrait"
            android:theme="@style/BaseUnityGameActivityTheme" >
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.MAIN" />
            </intent-filter>

            <meta-data
                android:name="unityplayer.UnityActivity"
                android:value="true" />
            <meta-data
                android:name="android.app.lib_name"
                android:value="game" />
            <meta-data
                android:name="WindowManagerPreference:FreeformWindowSize"
                android:value="@string/FreeformWindowSize_maximize" />
            <meta-data
                android:name="WindowManagerPreference:FreeformWindowOrientation"
                android:value="@string/FreeformWindowOrientation_portrait" />
            <meta-data
                android:name="notch_support"
                android:value="true" />

            <layout
                android:minHeight="300px"
                android:minWidth="400px" />
        </activity>
    </application>

</manifest>