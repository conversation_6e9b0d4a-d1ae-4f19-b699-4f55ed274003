{"root": [{"assemblyName": "OwnMatch3", "nameSpace": "OwnMatch3.Utils", "className": "DebugManager", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "OwnMatch3", "nameSpace": "OwnMatch3.Utils", "className": "DebugSettings", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "OwnMatch3", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__5800224013931567721", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "PrimeTween.Runtime", "nameSpace": "PrimeTween", "className": "PrimeTweenManager", "methodName": "beforeSceneLoad", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "UnityEngine.U2D.Animation", "className": "GpuDeformationSystem", "methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__12640072059193112690", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.2D.SpriteShape.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.AI.Navigation", "nameSpace": "Unity.AI.Navigation", "className": "NavMeshLink", "methodName": "ClearTrackedList", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.AI.Navigation", "nameSpace": "Unity.AI.Navigation", "className": "NavMeshModifier", "methodName": "ClearNavMeshModifiers", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.AI.Navigation", "nameSpace": "Unity.AI.Navigation", "className": "NavMeshModifierVolume", "methodName": "ClearNavMeshModifiers", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.AI.Navigation", "nameSpace": "Unity.AI.Navigation", "className": "NavMeshSurface", "methodName": "ClearNavMeshSurfaces", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.AppUI", "nameSpace": "Unity.AppUI.Core", "className": "AppUI", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.AppUI", "nameSpace": "Unity.AppUI.Core", "className": "AppUIManagerBehaviour", "methodName": "Create", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.AppUI", "nameSpace": "Unity.AppUI.Core", "className": "Platform", "methodName": "Initialize", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Burst", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1652832624114795843", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InferenceEngine", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1459105707999121466", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InferenceEngine", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem.ForUI", "nameSpace": "UnityEngine.InputSystem.Plugins.InputForUI", "className": "InputSystemProvider", "methodName": "Bootstrap", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.LevelPlay", "nameSpace": "", "className": "IronSourceInitilizer", "methodName": "Initilize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.LevelPlay", "nameSpace": "Unity.Services.LevelPlay", "className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Rendering.LightTransport.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__16164947281921951637", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__15867191014387474753", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Universal.2D.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core", "nameSpace": "Unity.Services.Core", "className": "UnityThreadUtils", "methodName": "CaptureUnityThreadInfo", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "TaskAsyncOperation", "methodName": "SetScheduler", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "CreateStaticInstance", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "EnableServicesInitializationAsync", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Registration", "nameSpace": "Unity.Services.Core.Registration", "className": "CorePackageInitializer", "methodName": "InitializeOnLoad", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}]}