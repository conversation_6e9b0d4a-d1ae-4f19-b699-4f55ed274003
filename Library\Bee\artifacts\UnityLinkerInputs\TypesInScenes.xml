<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="Match3.BonusGemBonusItem" preserve="nothing"/>
		<type fullname="Match3.ColorClean" preserve="nothing"/>
		<type fullname="Match3.GameManager" preserve="nothing"/>
		<type fullname="Match3.JumpingFish" preserve="nothing"/>
		<type fullname="Match3.LargeBomb" preserve="nothing"/>
		<type fullname="Match3.LineRocket" preserve="nothing"/>
		<type fullname="Match3.ShopItemBonusItem" preserve="nothing"/>
		<type fullname="Match3.ShopItemCoin" preserve="nothing"/>
		<type fullname="Match3.ShopItemLive" preserve="nothing"/>
		<type fullname="Match3.SmallBomb" preserve="nothing"/>
		<type fullname="Match3.UIHandler" preserve="nothing"/>
	</assembly>
	<assembly fullname="CFXRRuntime">
		<type fullname="CartoonFX.CFXR_Effect" preserve="nothing"/>
	</assembly>
	<assembly fullname="OwnMatch3">
		<type fullname="AIGenerationConfig" preserve="nothing"/>
		<type fullname="AILevelGenerator" preserve="nothing"/>
		<type fullname="BombBonus" preserve="nothing"/>
		<type fullname="BombXBonus" preserve="nothing"/>
		<type fullname="BonusGemEffects" preserve="nothing"/>
		<type fullname="BonusGemManager" preserve="nothing"/>
		<type fullname="CameraManager" preserve="nothing"/>
		<type fullname="ColorBonus" preserve="nothing"/>
		<type fullname="ComboManager" preserve="nothing"/>
		<type fullname="CurrencyAnimationManager" preserve="nothing"/>
		<type fullname="CurrencyAttractorUI" preserve="nothing"/>
		<type fullname="CurrencyTestController" preserve="nothing"/>
		<type fullname="DevsDaddy.GameShield.Core.GameShield" preserve="nothing"/>
		<type fullname="DevsDaddy.GameShield.Core.GameShieldConfig" preserve="nothing"/>
		<type fullname="DevsDaddy.GameShield.Demo.GameInstaller" preserve="nothing"/>
		<type fullname="EndGamePopupController" preserve="nothing"/>
		<type fullname="FishBonus" preserve="nothing"/>
		<type fullname="FPSCounter" preserve="nothing"/>
		<type fullname="FrameBuilder" preserve="nothing"/>
		<type fullname="GameProgressManager" preserve="nothing"/>
		<type fullname="GameUIDocument" preserve="nothing"/>
		<type fullname="GemAttractorUI" preserve="nothing"/>
		<type fullname="GemDestructionEffect" preserve="nothing"/>
		<type fullname="GemNew" preserve="nothing"/>
		<type fullname="GemSoundSystem" preserve="nothing"/>
		<type fullname="GetTimeNetInitializer" preserve="nothing"/>
		<type fullname="LevelLoader" preserve="nothing"/>
		<type fullname="LineRocketBonus" preserve="nothing"/>
		<type fullname="Match3.PreGamePopupController" preserve="nothing"/>
		<type fullname="Match3Board" preserve="nothing"/>
		<type fullname="ModernAILevelGeneratorWindow" preserve="nothing"/>
		<type fullname="MovesWarningAnimation" preserve="nothing"/>
		<type fullname="MoveToNextScene" preserve="nothing"/>
		<type fullname="ObjectPoolerSettings" preserve="nothing"/>
		<type fullname="ObstacleNew" preserve="nothing"/>
		<type fullname="ObstacleSoundSystem" preserve="nothing"/>
		<type fullname="OwnMatch3.AudioManager" preserve="nothing"/>
		<type fullname="OwnMatch3.GameStateManager" preserve="nothing"/>
		<type fullname="OwnMatch3.Monetization.AdConfig" preserve="nothing"/>
		<type fullname="OwnMatch3.Monetization.AdsManager" preserve="nothing"/>
		<type fullname="OwnMatch3.Shop.ShopConfiguration" preserve="nothing"/>
		<type fullname="OwnMatch3.Shop.ShopManager" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.DailyRewardsManager" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.EmailManager" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.EmailUIController" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.MenuUIController" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.MissionsManager" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.MissionsUIController" preserve="nothing"/>
		<type fullname="OwnMatch3.UI.Particles.SimpleParticleController" preserve="nothing"/>
		<type fullname="OwnMatch3.Utils.DebugSettings" preserve="nothing"/>
		<type fullname="RocketExplosionEffect" preserve="nothing"/>
		<type fullname="RocketTrailEffect" preserve="nothing"/>
		<type fullname="ScoreManager" preserve="nothing"/>
		<type fullname="ScoreProgressBarController" preserve="nothing"/>
		<type fullname="UIManager" preserve="nothing"/>
		<type fullname="UpdateGame" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.2D.Animation.Runtime">
		<type fullname="UnityEngine.U2D.Animation.BufferManager" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.Animation.DeformationManager" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.Animation.DeformationManagerUpdater" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.Animation.SpriteLibrary" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.Animation.SpriteLibraryAsset" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.Animation.SpriteResolver" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.Animation.SpriteSkin" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.2D.IK.Runtime">
		<type fullname="UnityEngine.U2D.IK.IKManager2D" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.IK.LimbSolver2D" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.2D.SpriteShape.Runtime">
		<type fullname="UnityEngine.U2D.SpriteShape" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.SpriteShapeController" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.SpriteShapeDefaultCreator" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.2D.Tilemap.Extras">
		<type fullname="UnityEngine.RuleTile" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.AppUI">
		<type fullname="Unity.AppUI.Core.AppUISettings" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputActionAsset" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputActionReference" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSettings" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSystemObject" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.RemoteInputPlayerConnection" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.UI.InputSystemUIInputModule" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.LevelPlay">
		<type fullname="IronSourceMediationSettings" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.ProbeVolumesOptions" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerBitField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerColor" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerContainer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFoldout" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerHBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerMessageBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObject" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectList" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerProgressBar" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerRenderingLayerField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerRow" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValue" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValueTuple" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector2" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector3" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector4" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.UIFoldout" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.VolumeProfile" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Universal.2D.Runtime">
		<type fullname="UnityEngine.Rendering.Universal.Light2D" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.Renderer2DData" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Universal.Runtime">
		<type fullname="UnityEngine.Rendering.Universal.Bloom" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ChannelMixer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ChromaticAberration" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ColorAdjustments" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ColorCurves" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ColorLookup" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.DepthOfField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.FilmGrain" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.LensDistortion" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.LiftGammaGain" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.MotionBlur" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.PaniniProjection" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.PostProcessData" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ScreenSpaceLensFlare" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.ShadowsMidtonesHighlights" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.SplitToning" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.Tonemapping" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.UniversalRenderPipelineGlobalSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.Vignette" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Universal.WhiteBalance" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.VisualEffectGraph.Runtime">
		<type fullname="UnityEngine.VFX.VFXRuntimeResources" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine">
		<type fullname="UnityEditor.Animations.AnimatorController" preserve="nothing"/>
		<type fullname="UnityEditor.AudioManager" preserve="nothing"/>
		<type fullname="UnityEditor.InputManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoScript" preserve="nothing"/>
		<type fullname="UnityEditor.Physics2DSettings" preserve="nothing"/>
		<type fullname="UnityEditor.PlayerSettings" preserve="nothing"/>
		<type fullname="UnityEditor.TagManager" preserve="nothing"/>
		<type fullname="UnityEditor.TimeManager" preserve="nothing"/>
		<type fullname="UnityEditor.VFXManager" preserve="nothing"/>
		<type fullname="UnityEngine.AnimationClip" preserve="nothing"/>
		<type fullname="UnityEngine.Animations.PositionConstraint" preserve="nothing"/>
		<type fullname="UnityEngine.Animator" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixer" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerSnapshot" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioResource" preserve="nothing"/>
		<type fullname="UnityEngine.AudioBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.AudioClip" preserve="nothing"/>
		<type fullname="UnityEngine.AudioListener" preserve="nothing"/>
		<type fullname="UnityEngine.AudioSource" preserve="nothing"/>
		<type fullname="UnityEngine.Behaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Camera" preserve="nothing"/>
		<type fullname="UnityEngine.Component" preserve="nothing"/>
		<type fullname="UnityEngine.ComputeShader" preserve="nothing"/>
		<type fullname="UnityEngine.Cubemap" preserve="nothing"/>
		<type fullname="UnityEngine.Font" preserve="nothing"/>
		<type fullname="UnityEngine.GameObject" preserve="nothing"/>
		<type fullname="UnityEngine.Grid" preserve="nothing"/>
		<type fullname="UnityEngine.GridLayout" preserve="nothing"/>
		<type fullname="UnityEngine.Light" preserve="nothing"/>
		<type fullname="UnityEngine.LightmapSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LineRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Material" preserve="nothing"/>
		<type fullname="UnityEngine.Mesh" preserve="nothing"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Motion" preserve="nothing"/>
		<type fullname="UnityEngine.Object" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystem" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystemRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.QualitySettings" preserve="nothing"/>
		<type fullname="UnityEngine.Renderer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.SortingGroup" preserve="nothing"/>
		<type fullname="UnityEngine.RenderSettings" preserve="nothing"/>
		<type fullname="UnityEngine.RenderTexture" preserve="nothing"/>
		<type fullname="UnityEngine.RuntimeAnimatorController" preserve="nothing"/>
		<type fullname="UnityEngine.Shader" preserve="nothing"/>
		<type fullname="UnityEngine.ShaderVariantCollection" preserve="nothing"/>
		<type fullname="UnityEngine.Sprite" preserve="nothing"/>
		<type fullname="UnityEngine.SpriteMask" preserve="nothing"/>
		<type fullname="UnityEngine.SpriteRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.TextAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Texture" preserve="nothing"/>
		<type fullname="UnityEngine.Texture2D" preserve="nothing"/>
		<type fullname="UnityEngine.Tilemaps.Tilemap" preserve="nothing"/>
		<type fullname="UnityEngine.Tilemaps.TilemapRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Transform" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.SpriteAtlas" preserve="nothing"/>
		<type fullname="UnityEngine.U2D.SpriteShapeRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.VFX.VFXRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.VFX.VisualEffect" preserve="nothing"/>
		<type fullname="UnityEngine.VFX.VisualEffectAsset" preserve="nothing"/>
		<type fullname="UnityEngine.VFX.VisualEffectObject" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.TextCoreTextEngineModule">
		<type fullname="UnityEngine.TextCore.Text.FontAsset" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.TilemapModule">
		<type fullname="UnityEngine.Tilemaps.Tile" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Button" preserve="nothing"/>
		<type fullname="UnityEngine.UI.CanvasScaler" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ContentSizeFitter" preserve="nothing"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="nothing"/>
		<type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Image" preserve="nothing"/>
		<type fullname="UnityEngine.UI.LayoutElement" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Mask" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Scrollbar" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ScrollRect" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Text" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Toggle" preserve="nothing"/>
		<type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UIElementsModule">
		<type fullname="UnityEngine.UIElements.PanelSettings" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.StyleSheet" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.ThemeStyleSheet" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.UIDocument" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.VisualTreeAsset" preserve="nothing"/>
	</assembly>
</linker>
