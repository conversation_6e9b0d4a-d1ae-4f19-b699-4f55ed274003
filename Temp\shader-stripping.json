{"totalVariantsIn": 4273, "totalVariantsOut": 2481, "shaders": [{"inputVariants": 10, "outputVariants": 0, "name": "Cartoon FX/Remaster/Particle Ubershader", "pipelines": [{"inputVariants": 10, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 10, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 3.4134}]}]}, {"inputVariants": 8, "outputVariants": 0, "name": "Particles/Standard Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0489}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0533}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Cartoon FX/Remaster/Particle Procedural Ring", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056900000000000006}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Sampling", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 3.7778}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/CanvasBackground", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.21880000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.092}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06420000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Stop NaN", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08420000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08940000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06430000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Shadow2DShadowGeometry", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Draw Geometry Shadow (R) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/CameraMotionBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059800000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0772}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0584}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera And Object Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera And Object Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera And Object Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0587}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/BillboardWavingDoublePass", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.15960000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Universal Render Pipeline/BokehDepthOfField", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06770000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0606}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0599}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0748}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0582}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0574}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.081}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0666}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0627}]}]}, {"inputVariants": 512, "outputVariants": 512, "name": "Hidden/Light2D", "pipelines": [{"inputVariants": 512, "outputVariants": 512, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 512, "outputVariants": 512, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.7155}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XROcclusionMesh", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1532}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Shadow2DShadowSprite", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Draw Sprite Shadow (R) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044500000000000005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Edge Adaptive Spatial Upsampling", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0608}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal/HDRDebugView", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0751}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0574}]}]}, {"inputVariants": 7, "outputVariants": 7, "name": "Hidden/Universal Render Pipeline/GaussianDepthOfField", "pipelines": [{"inputVariants": 7, "outputVariants": 7, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0599}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06620000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0656}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/Bloom", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0636}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0587}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06420000000000001}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/Universal Render Pipeline/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0702}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1165}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0824}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/LutBuilderLdr", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0587}]}]}, {"inputVariants": 33, "outputVariants": 33, "name": "Hidden/Universal Render Pipeline/LensFlareDataDriven", "pipelines": [{"inputVariants": 33, "outputVariants": 33, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0838}, {"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0844}, {"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0898}, {"inputVariants": 8, "outputVariants": 8, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0862}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0603}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/Universal Render Pipeline/LensFlareScreenSpace", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0648}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061000000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058600000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0603}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shadow2DUnshadowGeometry", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Geometry Unshadow (0) - Stencil: Ref 1, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Geometry Unshadow (B) - Stencil: Ref 0, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0627}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/CircularProgress", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}]}]}, {"inputVariants": 29, "outputVariants": 29, "name": "Hidden/Universal/CoreBlit", "pipelines": [{"inputVariants": 29, "outputVariants": 29, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0637}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0596}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0585}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0603}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0601}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0534}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0591}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0611}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0568}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0497}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0629}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0494}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059800000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0815}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0596}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0848}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0631}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062200000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059000000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0634}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/App UI/SVSquare", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}]}]}, {"inputVariants": 7, "outputVariants": 7, "name": "Hidden/Universal Render Pipeline/TemporalAA", "pipelines": [{"inputVariants": 7, "outputVariants": 7, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0594}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0587}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062200000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/PaniniProjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Panini Projection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0641}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal/BlitHDROverlay", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057100000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0347}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/ColorSwatch", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0519}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/LinearProgress", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0658}]}]}, {"inputVariants": 256, "outputVariants": 96, "name": "Hidden/Universal Render Pipeline/FinalPost", "pipelines": [{"inputVariants": 256, "outputVariants": 96, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2383}, {"inputVariants": 64, "outputVariants": 0, "variantName": "FinalPostXR (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1051}, {"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.2328}, {"inputVariants": 64, "outputVariants": 0, "variantName": "FinalPostXR (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.1061}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/App UI/ColorWheel", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0703}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ShadowProjected2D", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Projected Shadow (R) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0673}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Projected Unshadow (G) - Stencil: Ref 1, <PERSON><PERSON> Eq, <PERSON> Keep (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0626}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/Debug/DebugReplacement", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal/CoreBlitColorAndDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058800000000000005}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XRMirrorView", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06570000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/Universal Render Pipeline/CopyDepth", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0707}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shadow2DUnshadowSprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Sprite Unshadow (B) - Stencil: Ref 1, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0627}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Sprite Unshadow (B) - Stencil: Ref 0, <PERSON><PERSON> <PERSON>, <PERSON> Replace (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0674}]}]}, {"inputVariants": 11, "outputVariants": 11, "name": "Hidden/App UI/Box", "pipelines": [{"inputVariants": 11, "outputVariants": 11, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Clear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06860000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0661}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundColor (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0627}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundImage (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0731}, {"inputVariants": 2, "outputVariants": 2, "variantName": "InsetBoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06720000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Border (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0648}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Outline (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}]}]}, {"inputVariants": 26, "outputVariants": 6, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/WavingDoublePass", "pipelines": [{"inputVariants": 26, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 24, "outputVariants": 4, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061500000000000006}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Universal Render Pipeline/FallbackError", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062400000000000004}]}]}, {"inputVariants": 1920, "outputVariants": 720, "name": "Hidden/Universal Render Pipeline/UberPost", "pipelines": [{"inputVariants": 1920, "outputVariants": 720, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 960, "outputVariants": 720, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 3.1011}, {"inputVariants": 960, "outputVariants": 0, "variantName": "UberPostXR (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.6207}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Hidden/Universal Render Pipeline/Scaling Setup", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 6, "outputVariants": 4, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.084}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Universal Render Pipeline/LutBuilderHdr", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08220000000000001}]}]}, {"inputVariants": 18, "outputVariants": 8, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/Vertexlit", "pipelines": [{"inputVariants": 18, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 4, "variantName": "TerrainDetailVertex (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1202}, {"inputVariants": 8, "outputVariants": 2, "variantName": "TerrainDetailVertex - GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0805}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0636}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0631}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XRMotionVector", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "XR Camera MotionVectors (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0636}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/App UI/Mask", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050300000000000004}]}]}, {"inputVariants": 65, "outputVariants": 65, "name": "Shader Graphs/TileShader", "pipelines": [{"inputVariants": 65, "outputVariants": 65, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Sprite Lit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2265}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Sprite Normal (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0911}, {"inputVariants": 32, "outputVariants": 32, "variantName": "Sprite Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.17300000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/TextCore/Sprite", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/ReduceExpBias", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0666}]}]}, {"inputVariants": 43, "outputVariants": 43, "name": "Hidden/InferenceEngine/Activation", "pipelines": [{"inputVariants": 43, "outputVariants": 43, "pipeline": "", "variants": [{"inputVariants": 43, "outputVariants": 43, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.21480000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Dense", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0776}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Pad", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0902}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/Mat<PERSON>ul", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0655}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/RadialWrap/RadialWrap/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1077}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07100000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Transpose", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0711}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/ActivationInt", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0898}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Universal Render Pipeline/2D/Sprite-Unlit-Default", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0738}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06430000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Trilu", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06960000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/InstanceNormalizationTail", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Tile", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0661}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/TextureConversion/TensorToTexture", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10260000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/InferenceEngine/TextureTensorDataDownload", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0683}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/CumSum", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.14980000000000002}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombRed/Muzzle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07970000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07970000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/LayerNormalizationTail", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0889}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/IsInfNaN", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0675}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Up/DistoArrow", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0709}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0712}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/GatherElements", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08070000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Bubbles/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08030000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0792}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/TextureConversion/TextureToTensor", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11320000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/DepthwiseConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10260000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/ConvTranspose", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1233}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/DepthToSpace", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07980000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/SpaceToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0703}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/InferenceEngine/ScatterND", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 20, "outputVariants": 20, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.13190000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Reshape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0792}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/Conv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1032}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Hidden/InferenceEngine/GridSample", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 36, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.17980000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/TextureConversion/ComputeBufferToTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07060000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Gather", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0699}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Cast", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06670000000000001}]}]}, {"inputVariants": 33, "outputVariants": 33, "name": "Hidden/InferenceEngine/Broadcast", "pipelines": [{"inputVariants": 33, "outputVariants": 33, "pipeline": "", "variants": [{"inputVariants": 33, "outputVariants": 33, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1744}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/BatchNormalization", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0673}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rainbow/Strip/Output ParticleStrip Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.081}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0801}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Gemm", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0772}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/TextureTensorDataUpload", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06620000000000001}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Hidden/InferenceEngine/Reduce", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 18, "outputVariants": 18, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1292}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Softmax", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0736}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/RoiAlign", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0892}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Muzzle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0818}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0845}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Random", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1063}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/HardmaxEnd", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0644}]}]}, {"inputVariants": 40, "outputVariants": 40, "name": "Hidden/InferenceEngine/ScatterElements", "pipelines": [{"inputVariants": 40, "outputVariants": 40, "pipeline": "", "variants": [{"inputVariants": 40, "outputVariants": 40, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.20350000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/GatherND", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0656}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ConstantOfShape", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06420000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/Coin/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0782}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0881}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Where", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06430000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Copy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0813}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/RMSNormalizationTail", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0669}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "Hidden/InferenceEngine/Upsample", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 9, "outputVariants": 9, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0903}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/SliceSet", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07780000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Expand", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0702}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/InferenceEngine/GlobalPool", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0782}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/GroupedConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1062}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Loose/Bubbles/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08310000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0818}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Split", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0765}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062200000000000005}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Universal Render Pipeline/2D/Sprite-Lit-Default", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.16690000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0645}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09430000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Universal Render Pipeline/2D/Sprite-Mask", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0601}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0645}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049800000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Range", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0575}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/OneHot", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07060000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/InferenceEngine/Resize1D", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1091}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/LocalPool", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07980000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ScalarMad", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0695}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/ReduceIndices", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.095}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Slice", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0942}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ScaleBias", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0771}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Up/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0736}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0732}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/LayoutSwitchBlockedAxis", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09530000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/InferenceEngine/TopP", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1287}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombRed/Glow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0792}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08850000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Up/DistoTrail", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0742}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06910000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Fishes/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0792}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0806}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rainbow/Bubble/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0833}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0784}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Glow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0792}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08030000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/CoinGlow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08410000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0795}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Loose/Fishes/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08220000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0799}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Up/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0888}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1027}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombRed/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0852}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0814}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Down/DistorArrow", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0804}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07150000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Win/Trail Bodies/Output ParticleStrip Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0748}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0737}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rainbow/Cicle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11810000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0825}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0794}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.097}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/StaticGlow/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0792}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07780000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Loose/Trail Bodies/Output ParticleStrip Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0721}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0687}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Down/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0698}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.069}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Down/DistoTrail", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0746}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07440000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Stars/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.082}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0777}]}]}, {"inputVariants": 70, "outputVariants": 70, "name": "Hidden/VFX/VFX_Rainbow/Gem/Output Particle Shader Graph Quad - Sprite Lit", "pipelines": [{"inputVariants": 70, "outputVariants": 70, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 64, "outputVariants": 64, "variantName": "Sprite Lit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38020000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Sprite Normal (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0645}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08020000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_BombBlue/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08410000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0823}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Coin/Circle/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.084}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0853}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Loose/Gems/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.111}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Down/Output Particle Shader Graph Quad - Sprite Unlit", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0687}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Sprite Unlit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0699}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/BubbleUp/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08030000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0813}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Win/Gems/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0927}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0776}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/System (5)/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0787}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0796}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/BubbleDown/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1227}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060000000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/System (6)/Output Particle Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0592}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057100000000000005}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Trail Bodies/TrailUp", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057800000000000004}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1107}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Trail Bodies/Output ParticleStrip Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0873}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07930000000000001}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_UpDown/Trail Bodies (1)/TrailDown", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11230000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0766}]}]}, {"inputVariants": 16, "outputVariants": 4, "name": "Hidden/VFX/VFX_Rocket_Distortion_Sides/Trail Bodies (1)/Output ParticleStrip Unlit Quad", "pipelines": [{"inputVariants": 16, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 2, "variantName": "UniversalForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0903}, {"inputVariants": 8, "outputVariants": 2, "variantName": "Pass 3 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0784}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Cartoon FX/Remaster/Particle Procedural Ring", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "BASE (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10300000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BASE (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.069}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058}]}]}, {"inputVariants": 18, "outputVariants": 8, "name": "Cartoon FX/Remaster/Particle Ubershader", "pipelines": [{"inputVariants": 18, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "BASE_URP (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0671}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BASE_URP (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0763}, {"inputVariants": 10, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058600000000000006}]}]}, {"inputVariants": 20, "outputVariants": 12, "name": "Particles/Standard Unlit", "pipelines": [{"inputVariants": 20, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062400000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 4 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09680000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Mobile/Particles/Additive", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06470000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Mobile/Particles/Alpha Blended", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06720000000000001}]}]}, {"inputVariants": 6, "outputVariants": 4, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 6, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0695}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0629}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0441}]}]}, {"inputVariants": 17, "outputVariants": 17, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 17, "outputVariants": 17, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07740000000000001}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.098}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07730000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07490000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0665}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0669}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0651}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0655}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0694}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.07060000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0634}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0601}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0585}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0743}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.07970000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.1038}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.075}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0776}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.063}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0654}]}]}, {"inputVariants": 27, "outputVariants": 27, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 27, "outputVariants": 27, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1516}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0679}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0711}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0684}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0684}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0718}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08120000000000001}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0848}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0728}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0646}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06570000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0666}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0621}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0616}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0629}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0751}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0626}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.062400000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0529}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.08600000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.07830000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0579}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.096}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059300000000000005}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061200000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Sprites/Default", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1051}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07880000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "UI/Default", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0729}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0757}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0795}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0649}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061200000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0625}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0637}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0877}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.060000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.09380000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.060000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0649}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.10500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0635}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.052700000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0629}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060500000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.062}]}]}, {"inputVariants": 1, "outputVariants": 1, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 1, "outputVariants": 1, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}]}]}]}