<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":unityLibrary:GooglePlayGamesManifest.androidlib" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":unityLibrary:IronSource.androidlib" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\IronSource.androidlib\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="com.unity3d.ads:unity-ads:4.15.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\assets"><file name="ad-viewer/omid-session-client-v1.js" path="C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\assets\ad-viewer\omid-session-client-v1.js"/><file name="ad-viewer/omsdk-v1.js" path="C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\assets\ad-viewer\omsdk-v1.js"/></source></dataSet><dataSet config=":unityLibrary" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="bin/Data/boot.config" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\boot.config"/><file name="bin/Data/data.unity3d" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\data.unity3d"/><file name="bin/Data/Managed/Metadata/global-metadata.dat" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\Managed\Metadata\global-metadata.dat"/><file name="bin/Data/Managed/Resources/mscorlib.dll-resources.dat" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\Managed\Resources\mscorlib.dll-resources.dat"/><file name="bin/Data/Managed/Resources/Newtonsoft.Json.dll-resources.dat" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\Managed\Resources\Newtonsoft.Json.dll-resources.dat"/><file name="bin/Data/Managed/Resources/System.Data.dll-resources.dat" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\Managed\Resources\System.Data.dll-resources.dat"/><file name="bin/Data/Managed/Resources/System.Drawing.dll-resources.dat" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\Managed\Resources\System.Drawing.dll-resources.dat"/><file name="bin/Data/Resources/unity default resources" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\Resources\unity default resources"/><file name="bin/Data/RuntimeInitializeOnLoads.json" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\RuntimeInitializeOnLoads.json"/><file name="bin/Data/ScriptingAssemblies.json" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\ScriptingAssemblies.json"/><file name="bin/Data/unity_app_guid" path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\library_assets\release\packageReleaseAssets\out\bin\Data\unity_app_guid"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\release\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>