{ "pid": 82960, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 82960, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 82960, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 82960, "tid": 12884901888, "ts": 1753393190198427, "dur": 6670, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 82960, "tid": 12884901888, "ts": 1753393193077449, "dur": 108564, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 82960, "tid": 12884901888, "ts": 1753393198269735, "dur": 564046, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 82960, "tid": 1, "ts": 1753393199359373, "dur": 2167, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 82960, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 82960, "tid": 8589934592, "ts": 1753393192018454, "dur": 132951, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 82960, "tid": 1, "ts": 1753393199361543, "dur": 31, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 82960, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 82960, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 82960, "tid": 1, "ts": 1753393187669503, "dur": 11678126, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 82960, "tid": 1, "ts": 1753393187671343, "dur": 157693, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393187773851, "dur": 31453, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393187829038, "dur": 30346, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393187963152, "dur": 157131, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393188122071, "dur": 212102, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393188334234, "dur": 1556797, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393189891055, "dur": 416177, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190307248, "dur": 432008, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190739270, "dur": 4383, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190743667, "dur": 1160, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190744832, "dur": 2182, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190747019, "dur": 431, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190747457, "dur": 15974, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190763437, "dur": 317, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190763756, "dur": 149, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190763907, "dur": 60, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190763968, "dur": 186, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190764156, "dur": 138, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190764295, "dur": 51, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190764347, "dur": 474, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190764825, "dur": 21103, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190785935, "dur": 449, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190786396, "dur": 8826, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190795241, "dur": 7910, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190803158, "dur": 260, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190803422, "dur": 56, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190803478, "dur": 1867, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190805350, "dur": 378, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190805730, "dur": 113, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190805844, "dur": 78, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190805922, "dur": 52, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190805975, "dur": 796, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190806775, "dur": 196, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190806973, "dur": 3846, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190810824, "dur": 252, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190811082, "dur": 3147, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190814233, "dur": 209, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190814444, "dur": 3514, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190817961, "dur": 151, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190818114, "dur": 344, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190818460, "dur": 96, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190818556, "dur": 394, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190818951, "dur": 122, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190819074, "dur": 3807, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190822886, "dur": 3397, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190826287, "dur": 858, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190827146, "dur": 4201, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190831351, "dur": 912, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190832265, "dur": 2603, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190834870, "dur": 612, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190835486, "dur": 1849, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190837338, "dur": 938, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190838290, "dur": 1794, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190840093, "dur": 1608, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190841711, "dur": 912, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190842631, "dur": 36958, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190879598, "dur": 145, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190879745, "dur": 154, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190879899, "dur": 7632, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190887536, "dur": 1376, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190888916, "dur": 643, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393190889569, "dur": 138761, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191028336, "dur": 5663, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191034002, "dur": 2593, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191036605, "dur": 142516, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191179137, "dur": 445, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191179591, "dur": 863, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191180467, "dur": 374, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191180851, "dur": 836, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191181694, "dur": 623, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191182324, "dur": 4303, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191186630, "dur": 99, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191186730, "dur": 197, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191186928, "dur": 15, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191186943, "dur": 469, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191187413, "dur": 355, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191187768, "dur": 49, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191187818, "dur": 46, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191187865, "dur": 119, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191187984, "dur": 26, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188011, "dur": 13, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188024, "dur": 22, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188047, "dur": 398, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188446, "dur": 18, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188464, "dur": 9, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188474, "dur": 4, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188479, "dur": 2, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188481, "dur": 1, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188483, "dur": 444, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188928, "dur": 1, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191188934, "dur": 96403, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191285362, "dur": 15851, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191301233, "dur": 173259, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191474513, "dur": 300, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191474822, "dur": 117659, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191592521, "dur": 23330, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191615868, "dur": 11563, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191627449, "dur": 17543, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191645016, "dur": 33537, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393191678571, "dur": 5402441, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197081031, "dur": 941, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197081980, "dur": 6739, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197088725, "dur": 94662, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197183398, "dur": 5152, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197188561, "dur": 4842, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197193414, "dur": 2462, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197195882, "dur": 2577, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197198464, "dur": 3161, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197201629, "dur": 145, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197201778, "dur": 430, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197202213, "dur": 527, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393197202746, "dur": 2083503, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393199286267, "dur": 37028, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393199323308, "dur": 297, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393199325939, "dur": 21482, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393199347632, "dur": 2620, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393199361577, "dur": 153, "ph": "X", "name": "", "args": {} },
{ "pid": 82960, "tid": 1, "ts": 1753393199358327, "dur": 4362, "ph": "X", "name": "Write chrome-trace events", "args": {} },
