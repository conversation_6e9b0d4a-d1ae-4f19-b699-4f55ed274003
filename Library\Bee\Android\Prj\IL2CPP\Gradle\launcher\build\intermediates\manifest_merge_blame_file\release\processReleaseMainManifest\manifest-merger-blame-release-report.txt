1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.PhantomTeam.PufflandAdventure"
4    android:installLocation="preferExternal"
5    android:versionCode="11"
6    android:versionName="0.2.01" >
7
8    <uses-sdk
9        android:minSdkVersion="23"
10        android:targetSdkVersion="36" />
11
12    <supports-screens
12-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-163
13        android:anyDensity="true"
13-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:135-160
14        android:largeScreens="true"
14-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:78-105
15        android:normalScreens="true"
15-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-77
16        android:smallScreens="true"
16-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:21-48
17        android:xlargeScreens="true" />
17-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:106-134
18
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-66
19-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-63
20    <uses-permission android:name="android.permission.INTERNET" />
20-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-67
20-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-64
21
22    <uses-feature android:glEsVersion="0x00030000" />
22-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-54
22-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:19-51
23    <uses-feature
23-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-14:36
24        android:name="android.hardware.touchscreen"
24-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-52
25        android:required="false" />
25-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-33
26    <uses-feature
26-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:5-17:36
27        android:name="android.hardware.touchscreen.multitouch"
27-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-63
28        android:required="false" />
28-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:9-33
29    <uses-feature
29-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:5-20:36
30        android:name="android.hardware.touchscreen.multitouch.distinct"
30-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-72
31        android:required="false" />
31-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-33
32
33    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Permission will be merged into the manifest of the hosting app. -->
33-->[com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:7:5-79
33-->[com.google.android.gms:play-services-ads-identifier:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\253844010a2b1f920fcaa65c5e03756e\transformed\jetified-play-services-ads-identifier-18.1.0\AndroidManifest.xml:7:22-76
34    <!-- Is required to launch foreground extraction service for targetSdkVersion 28+. -->
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Is required to launch foreground extraction service for targetSdkVersion 34+. -->
35-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:13:5-77
35-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:13:22-74
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
36-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:15:5-87
36-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:15:22-84
37
38    <queries>
38-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:8:5-12:15
39        <intent>
39-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:9:9-11:18
40            <action android:name="com.attribution.REFERRAL_PROVIDER" />
40-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:13-72
40-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:21-69
41        </intent>
42
43        <package android:name="com.google.android.gms" />
43-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:8:9-58
43-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:8:18-55
44        <package android:name="com.android.vending" />
44-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:9:9-55
44-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:9:18-52
45    </queries>
46
47    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
47-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:14:5-79
47-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:14:22-76
48    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
48-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:5-83
48-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:22-80
49    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
49-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:5-88
49-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:22-85
50    <uses-permission android:name="android.permission.WAKE_LOCK" />
50-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
50-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
51    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
51-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
51-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:22-78
52
53    <permission
53-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
54        android:name="com.PhantomTeam.PufflandAdventure.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.PhantomTeam.PufflandAdventure.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
58
59    <application
59-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-126
60        android:allowBackup="false"
60-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:9-36
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\38f67d84e234ba470211615c7ce575fe\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
62        android:enableOnBackInvokedCallback="false"
62-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:9-52
63        android:extractNativeLibs="true"
64        android:icon="@mipmap/app_icon"
64-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:16-47
65        android:label="@string/app_name"
65-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:48-80
66        android:roundIcon="@mipmap/app_icon_round"
66-->F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:81-123
67        android:usesCleartextTraffic="false" >
67-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-45
68        <meta-data
68-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:9-29:70
69            android:name="com.google.android.gms.ads.APPLICATION_ID"
69-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-69
70            android:value="ca-app-pub-3940256099942544~3347511713" />
70-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-67
71        <meta-data
71-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:9-32:33
72            android:name="unity.splash-mode"
72-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-45
73            android:value="0" />
73-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:13-30
74        <meta-data
74-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-35:36
75            android:name="unity.splash-enable"
75-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-47
76            android:value="True" />
76-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-33
77        <meta-data
77-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:9-38:36
78            android:name="unity.launch-fullscreen"
78-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-51
79            android:value="True" />
79-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-33
80        <meta-data
80-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:9-41:36
81            android:name="unity.render-outside-safearea"
81-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-57
82            android:value="True" />
82-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:13-33
83        <meta-data
83-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:50
84            android:name="notch.config"
84-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-40
85            android:value="portrait|landscape" />
85-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-47
86        <meta-data
86-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:9-47:36
87            android:name="unity.auto-report-fully-drawn"
87-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:13-57
88            android:value="true" />
88-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-33
89        <meta-data
89-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:9-50:36
90            android:name="unity.auto-set-game-state"
90-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-53
91            android:value="true" />
91-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-33
92        <meta-data
92-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:51:9-53:36
93            android:name="unity.strip-engine-code"
93-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:52:13-51
94            android:value="true" />
94-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:53:13-33
95
96        <activity
96-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:55:9-89:20
97            android:name="com.unity3d.player.appui.AppUIGameActivity"
97-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:56:13-70
98            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
98-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:57:13-194
99            android:exported="true"
99-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:58:13-36
100            android:hardwareAccelerated="false"
100-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:59:13-48
101            android:launchMode="singleTask"
101-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:60:13-44
102            android:resizeableActivity="true"
102-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:61:13-46
103            android:screenOrientation="userPortrait"
103-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:62:13-53
104            android:theme="@style/BaseUnityGameActivityTheme" >
104-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:63:13-62
105            <intent-filter>
105-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:64:13-68:29
106                <category android:name="android.intent.category.LAUNCHER" />
106-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:65:17-77
106-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:65:27-74
107
108                <action android:name="android.intent.action.MAIN" />
108-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:67:17-69
108-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:67:25-66
109            </intent-filter>
110
111            <meta-data
111-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:70:13-72:40
112                android:name="unityplayer.UnityActivity"
112-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:71:17-57
113                android:value="true" />
113-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:72:17-37
114            <meta-data
114-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:73:13-75:40
115                android:name="android.app.lib_name"
115-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:74:17-52
116                android:value="game" />
116-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:75:17-37
117            <meta-data
117-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:76:13-78:71
118                android:name="WindowManagerPreference:FreeformWindowSize"
118-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:77:17-74
119                android:value="@string/FreeformWindowSize_maximize" />
119-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:78:17-68
120            <meta-data
120-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:79:13-81:78
121                android:name="WindowManagerPreference:FreeformWindowOrientation"
121-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:80:17-81
122                android:value="@string/FreeformWindowOrientation_portrait" />
122-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:81:17-75
123            <meta-data
123-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:82:13-84:40
124                android:name="notch_support"
124-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:83:17-45
125                android:value="true" />
125-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:84:17-37
126
127            <layout
127-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:86:13-88:44
128                android:minHeight="300px"
128-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:87:17-42
129                android:minWidth="400px" />
129-->[:unityLibrary] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:88:17-41
130        </activity> <!-- The services will be merged into the manifest of the hosting app. -->
131        <service
131-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:20:9-27:19
132            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
132-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:21:13-94
133            android:enabled="false"
133-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:22:13-36
134            android:exported="true" >
134-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:23:13-36
135            <meta-data
135-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:24:13-26:41
136                android:name="com.google.android.play.core.assetpacks.versionCode"
136-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:25:17-83
137                android:value="20100" />
137-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:26:17-38
138        </service>
139        <service
139-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:28:9-32:56
140            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
140-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:29:13-95
141            android:enabled="false"
141-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:30:13-36
142            android:exported="false"
142-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:31:13-37
143            android:foregroundServiceType="dataSync" /> <!-- The activities will be merged into the manifest of the hosting app. -->
143-->[com.google.android.play:asset-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa16657d9627cebbea2303e398f4c42a\transformed\jetified-asset-delivery-2.1.0\AndroidManifest.xml:32:13-53
144        <activity
144-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
145            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
145-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
146            android:exported="false"
146-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
147            android:stateNotNeeded="true"
147-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
148            android:theme="@style/Theme.PlayCore.Transparent" />
148-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd6faf7814b28a0a7c03912d1fdf5c7c\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
149        <activity
149-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:9:9-17:20
150            android:name="com.google.games.bridge.GenericResolutionActivity"
150-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:10:13-77
151            android:exported="false"
151-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:11:13-37
152            android:label="GenericResolutionActivity"
152-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:12:13-54
153            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" >
153-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:13:13-83
154            <meta-data
154-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:14:13-16:40
155                android:name="instantapps.clients.allowed"
155-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:15:17-59
156                android:value="true" />
156-->[com.google.games:gpgs-plugin-support:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5ad4be011b6d6f61903e7e1c3b25ce3e\transformed\jetified-gpgs-plugin-support-2.0.0\AndroidManifest.xml:16:17-37
157        </activity>
158        <activity
158-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:21:9-26:74
159            android:name="com.unity3d.services.ads.adunit.AdUnitActivity"
159-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:22:13-74
160            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
160-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:23:13-170
161            android:exported="false"
161-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:24:13-37
162            android:hardwareAccelerated="true"
162-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:25:13-47
163            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
163-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:26:13-71
164        <activity
164-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:27:9-32:86
165            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentActivity"
165-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:28:13-85
166            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
166-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:29:13-170
167            android:exported="false"
167-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:30:13-37
168            android:hardwareAccelerated="true"
168-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:31:13-47
169            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
169-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:32:13-83
170        <activity
170-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:33:9-38:86
171            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity"
171-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:34:13-93
172            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
172-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:35:13-170
173            android:exported="false"
173-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:36:13-37
174            android:hardwareAccelerated="false"
174-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:37:13-48
175            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
175-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:38:13-83
176        <activity
176-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:39:9-44:74
177            android:name="com.unity3d.services.ads.adunit.AdUnitSoftwareActivity"
177-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:40:13-82
178            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
178-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:41:13-170
179            android:exported="false"
179-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:42:13-37
180            android:hardwareAccelerated="false"
180-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:43:13-48
181            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
181-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:44:13-71
182        <activity
182-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:45:9-50:74
183            android:name="com.unity3d.ads.adplayer.FullScreenWebViewDisplay"
183-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:46:13-77
184            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
184-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:47:13-170
185            android:exported="false"
185-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:48:13-37
186            android:hardwareAccelerated="true"
186-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:49:13-47
187            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
187-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:50:13-71
188
189        <provider
189-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:52:9-62:20
190            android:name="androidx.startup.InitializationProvider"
190-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:53:13-67
191            android:authorities="com.PhantomTeam.PufflandAdventure.androidx-startup"
191-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:54:13-68
192            android:exported="false" >
192-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
193            <meta-data
193-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:56:13-58:52
194                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
194-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:57:17-78
195                android:value="androidx.startup" />
195-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:58:17-49
196            <meta-data
196-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:59:13-61:52
197                android:name="com.unity3d.services.core.configuration.AdsSdkInitializer"
197-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:60:17-89
198                android:value="androidx.startup" />
198-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f13d9874a6bb46f0a33b9cd3ac0b5079\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:61:17-49
199            <meta-data
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
200                android:name="androidx.emoji2.text.EmojiCompatInitializer"
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
201                android:value="androidx.startup" />
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\090ce07e4efa029fdc4271658553bc51\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
202            <meta-data
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
203                android:name="androidx.work.WorkManagerInitializer"
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
204                android:value="androidx.startup" />
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
205            <meta-data
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
206                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
207                android:value="androidx.startup" />
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
208        </provider>
209
210        <activity
210-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:12:9-16:63
211            android:name="com.ironsource.sdk.controller.ControllerActivity"
211-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:13:13-76
212            android:configChanges="orientation|screenSize"
212-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:14:13-59
213            android:hardwareAccelerated="true"
213-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:15:13-47
214            android:theme="@android:style/Theme.NoTitleBar" />
214-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:16:13-60
215        <activity
215-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:17:9-21:75
216            android:name="com.ironsource.sdk.controller.InterstitialActivity"
216-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:18:13-78
217            android:configChanges="orientation|screenSize"
217-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:19:13-59
218            android:hardwareAccelerated="true"
218-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:20:13-47
219            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
219-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:21:13-72
220        <activity
220-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:22:9-26:75
221            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
221-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:23:13-73
222            android:configChanges="orientation|screenSize"
222-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:24:13-59
223            android:hardwareAccelerated="true"
223-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:25:13-47
224            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
224-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:26:13-72
225        <activity
225-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:27:9-36:20
226            android:name="com.ironsource.mediationsdk.testSuite.TestSuiteActivity"
226-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:28:13-83
227            android:configChanges="orientation|screenSize"
227-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:29:13-59
228            android:exported="false"
228-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:30:13-37
229            android:hardwareAccelerated="true"
229-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:31:13-47
230            android:theme="@android:style/Theme.NoTitleBar" >
230-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:32:13-60
231            <meta-data
231-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:33:13-35:40
232                android:name="android.webkit.WebView.EnableSafeBrowsing"
232-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:34:17-73
233                android:value="true" />
233-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:35:17-37
234        </activity>
235
236        <provider
236-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:38:9-41:40
237            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider"
237-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:39:13-80
238            android:authorities="com.PhantomTeam.PufflandAdventure.IronsourceLifecycleProvider"
238-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:40:13-79
239            android:exported="false" />
239-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:41:13-37
240        <provider
240-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:42:9-45:40
241            android:name="com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider"
241-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:43:13-87
242            android:authorities="com.PhantomTeam.PufflandAdventure.LevelPlayActivityLifecycleProvider"
242-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:44:13-86
243            android:exported="false" /> <!-- The space in these forces it to be interpreted as a string vs. int -->
243-->[com.unity3d.ads-mediation:mediation-sdk:8.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b619e24824560d5d58a3c53651fefebd\transformed\jetified-mediation-sdk-8.10.0\AndroidManifest.xml:45:13-37
244        <meta-data
244-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-18:48
245            android:name="com.google.android.gms.games.APP_ID"
245-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-63
246            android:value="\u00327067140321" /> <!-- Keep track of which plugin is being used -->
246-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-45
247        <meta-data
247-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-23:42
248            android:name="com.google.android.gms.games.unityVersion"
248-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-69
249            android:value="\u0032.0.0" />
249-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-39
250
251        <activity
251-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:9-27:86
252            android:name="com.google.games.bridge.NativeBridgeActivity"
252-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-72
253            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
253-->[:unityLibrary:GooglePlayGamesManifest.androidlib] F:\Match2D\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GooglePlayGamesManifest.androidlib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-83
254
255        <provider
255-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:13:9-17:38
256            android:name="com.google.android.gms.games.provider.PlayGamesInitProvider"
256-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:14:13-87
257            android:authorities="com.PhantomTeam.PufflandAdventure.playgamesinitprovider"
257-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:15:13-73
258            android:exported="false"
258-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:16:13-37
259            android:initOrder="99" />
259-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:17:13-35
260
261        <activity
261-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:19:9-22:62
262            android:name="com.google.android.gms.games.internal.v2.resolution.GamesResolutionActivity"
262-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:20:13-103
263            android:exported="false"
263-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:21:13-37
264            android:theme="@style/Theme.Games.Transparent" />
264-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:22:13-59
265        <activity
265-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:23:9-26:62
266            android:name="com.google.android.gms.games.internal.v2.appshortcuts.PlayGamesAppShortcutsActivity"
266-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:24:13-111
267            android:exported="true"
267-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:25:13-36
268            android:theme="@style/Theme.Games.Transparent" />
268-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:26:13-59
269
270        <meta-data
270-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:28:9-30:62
271            android:name="com.google.android.gms.games.version"
271-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:29:13-64
272            android:value="@string/play_games_sdk_version" />
272-->[com.google.android.gms:play-services-games-v2:20.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5d9e677a0fad52e699ee987ed2bc3d7\transformed\jetified-play-services-games-v2-20.1.2\AndroidManifest.xml:30:13-59
273
274        <service
274-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:10:9-13:105
275            android:name="com.google.android.gms.nearby.exposurenotification.WakeUpService"
275-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:11:13-92
276            android:exported="true"
276-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:12:13-36
277            android:permission="com.google.android.gms.nearby.exposurenotification.EXPOSURE_CALLBACK" />
277-->[com.google.android.gms:play-services-nearby:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\838d5169b7b23486ea8ff7c7683726af\transformed\jetified-play-services-nearby-18.5.0\AndroidManifest.xml:13:13-102
278
279        <activity
279-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
280            android:name="com.google.android.gms.common.api.GoogleApiActivity"
280-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
281            android:exported="false"
281-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
282            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
282-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62592ff0bfa9a791250bc84e74e6fc93\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
283
284        <meta-data
284-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
285            android:name="com.google.android.gms.version"
285-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
286            android:value="@integer/google_play_services_version" />
286-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\93ad3fd77c199090ce77aa90bdfe079f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
287
288        <service
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
289            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
290            android:directBootAware="false"
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
291            android:enabled="@bool/enable_system_alarm_service_default"
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
292            android:exported="false" />
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
293        <service
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
294            android:name="androidx.work.impl.background.systemjob.SystemJobService"
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
295            android:directBootAware="false"
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
296            android:enabled="@bool/enable_system_job_service_default"
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
297            android:exported="true"
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
298            android:permission="android.permission.BIND_JOB_SERVICE" />
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
299        <service
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
300            android:name="androidx.work.impl.foreground.SystemForegroundService"
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
302            android:enabled="@bool/enable_system_foreground_service_default"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
303            android:exported="false" />
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
304
305        <receiver
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
306            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
307            android:directBootAware="false"
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
308            android:enabled="true"
308-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
309            android:exported="false" />
309-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
310        <receiver
310-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
311            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
311-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
313            android:enabled="false"
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
314            android:exported="false" >
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
315            <intent-filter>
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
316                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
317                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
318            </intent-filter>
319        </receiver>
320        <receiver
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
321            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
323            android:enabled="false"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
324            android:exported="false" >
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
325            <intent-filter>
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
326                <action android:name="android.intent.action.BATTERY_OKAY" />
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
327                <action android:name="android.intent.action.BATTERY_LOW" />
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
328            </intent-filter>
329        </receiver>
330        <receiver
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
331            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
333            android:enabled="false"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
334            android:exported="false" >
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
335            <intent-filter>
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
336                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
337                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
338            </intent-filter>
339        </receiver>
340        <receiver
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
341            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
342            android:directBootAware="false"
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
343            android:enabled="false"
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
344            android:exported="false" >
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
345            <intent-filter>
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
346                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
347            </intent-filter>
348        </receiver>
349        <receiver
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
350            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
352            android:enabled="false"
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
353            android:exported="false" >
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
354            <intent-filter>
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
355                <action android:name="android.intent.action.BOOT_COMPLETED" />
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
356                <action android:name="android.intent.action.TIME_SET" />
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
357                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
358            </intent-filter>
359        </receiver>
360        <receiver
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
361            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
362            android:directBootAware="false"
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
363            android:enabled="@bool/enable_system_alarm_service_default"
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
364            android:exported="false" >
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
365            <intent-filter>
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
366                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
367            </intent-filter>
368        </receiver>
369        <receiver
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
370            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
371            android:directBootAware="false"
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
372            android:enabled="true"
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
373            android:exported="true"
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
374            android:permission="android.permission.DUMP" >
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
375            <intent-filter>
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
376                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0542d31c6571b24e51fa9b0dd291c530\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
377            </intent-filter>
378        </receiver>
379        <receiver
379-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
380            android:name="androidx.profileinstaller.ProfileInstallReceiver"
380-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
381            android:directBootAware="false"
381-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
382            android:enabled="true"
382-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
383            android:exported="true"
383-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
384            android:permission="android.permission.DUMP" >
384-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
386                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
386-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
386-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
387            </intent-filter>
388            <intent-filter>
388-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
389                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
389-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
389-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
390            </intent-filter>
391            <intent-filter>
391-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
392                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
392-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
392-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
393            </intent-filter>
394            <intent-filter>
394-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
395                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
395-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
395-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d11e4da87d865425f9d6c22f3eb39b1\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
396            </intent-filter>
397        </receiver>
398
399        <service
399-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
400            android:name="androidx.room.MultiInstanceInvalidationService"
400-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
401            android:directBootAware="true"
401-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
402            android:exported="false" />
402-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\499e3ad166e4574b3b6c58c44be633c0\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
403    </application>
404
405</manifest>
